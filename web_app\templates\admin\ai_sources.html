{% extends "base.html" %}

{% block title %}AI源管理 - AI工具新闻查找平台{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-robot me-2"></i>AI源管理
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-primary" onclick="showAddModal()">
                <i class="fas fa-plus me-1"></i>添加AI源
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshTable()">
                <i class="fas fa-sync-alt me-1"></i>刷新
            </button>
        </div>
    </div>
</div>

<!-- AI源列表 -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">AI源列表</h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered" id="aiSourcesTable" width="100%" cellspacing="0">
                <thead>
                    <tr>
                        <th>名称</th>
                        <th>模型</th>
                        <th>端点URL</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for source in sources %}
                    <tr>
                        <td>{{ source.name }}</td>
                        <td>{{ source.model_name }}</td>
                        <td>
                            <span class="text-truncate d-inline-block" style="max-width: 200px;" title="{{ source.endpoint_url }}">
                                {{ source.endpoint_url }}
                            </span>
                        </td>
                        <td>
                            {% if source.enabled %}
                                <span class="badge bg-success">启用</span>
                            {% else %}
                                <span class="badge bg-secondary">禁用</span>
                            {% endif %}
                        </td>
                        <td>{{ source.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <button type="button" class="btn btn-outline-info" onclick="testConnection({{ source.id }})">
                                    <i class="fas fa-plug"></i>
                                </button>
                                <button type="button" class="btn btn-outline-primary" onclick="editSource({{ source.id }})">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" class="btn btn-outline-danger" onclick="deleteSource({{ source.id }})">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 添加/编辑AI源模态框 -->
<div class="modal fade" id="aiSourceModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="aiSourceModalTitle">添加AI源</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="aiSourceForm">
                    <input type="hidden" id="sourceId" name="sourceId">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sourceName" class="form-label">名称 *</label>
                                <input type="text" class="form-control" id="sourceName" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="modelName" class="form-label">模型名称 *</label>
                                <input type="text" class="form-control" id="modelName" name="model_name" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="endpointUrl" class="form-label">端点URL *</label>
                        <input type="url" class="form-control" id="endpointUrl" name="endpoint_url" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="apiKey" class="form-label">API密钥</label>
                        <input type="password" class="form-control" id="apiKey" name="api_key">
                        <div class="form-text">留空表示不修改现有密钥</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">描述</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="enabled" name="enabled" checked>
                            <label class="form-check-label" for="enabled">启用此AI源</label>
                        </div>
                    </div>
                    
                    <!-- 配置参数 -->
                    <div class="mb-3">
                        <label class="form-label">配置参数</label>
                        <div class="row">
                            <div class="col-md-4">
                                <label for="timeout" class="form-label">超时时间(秒)</label>
                                <input type="number" class="form-control" id="timeout" name="timeout" value="60">
                            </div>
                            <div class="col-md-4">
                                <label for="maxTokens" class="form-label">最大Token数</label>
                                <input type="number" class="form-control" id="maxTokens" name="max_tokens" value="4000">
                            </div>
                            <div class="col-md-4">
                                <label for="temperature" class="form-label">Temperature</label>
                                <input type="number" class="form-control" id="temperature" name="temperature" 
                                       value="0.1" step="0.1" min="0" max="2">
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveSource()">保存</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentSourceId = null;

function showAddModal() {
    currentSourceId = null;
    $('#aiSourceModalTitle').text('添加AI源');
    $('#aiSourceForm')[0].reset();
    $('#sourceId').val('');
    $('#enabled').prop('checked', true);
    $('#aiSourceModal').modal('show');
}

function editSource(sourceId) {
    currentSourceId = sourceId;
    $('#aiSourceModalTitle').text('编辑AI源');
    
    // 获取AI源详情
    $.ajax({
        url: `/admin/api/ai-sources/${sourceId}`,
        method: 'GET',
        success: function(response) {
            if (response.success) {
                const source = response.data;
                $('#sourceId').val(source.id);
                $('#sourceName').val(source.name);
                $('#modelName').val(source.model_name);
                $('#endpointUrl').val(source.endpoint_url);
                $('#description').val(source.description || '');
                $('#enabled').prop('checked', source.enabled);
                
                // 填充配置参数
                const config = source.config_params || {};
                $('#timeout').val(config.timeout || 60);
                $('#maxTokens').val(config.max_tokens || 4000);
                $('#temperature').val(config.temperature || 0.1);
                
                $('#aiSourceModal').modal('show');
            } else {
                showAlert(response.message, 'danger');
            }
        },
        error: function() {
            showAlert('获取AI源信息失败', 'danger');
        }
    });
}

function saveSource() {
    const formData = {
        name: $('#sourceName').val().trim(),
        model_name: $('#modelName').val().trim(),
        endpoint_url: $('#endpointUrl').val().trim(),
        api_key: $('#apiKey').val(),
        description: $('#description').val().trim(),
        enabled: $('#enabled').is(':checked'),
        config_params: {
            timeout: parseInt($('#timeout').val()) || 60,
            max_tokens: parseInt($('#maxTokens').val()) || 4000,
            temperature: parseFloat($('#temperature').val()) || 0.1
        }
    };
    
    // 验证必要字段
    if (!formData.name || !formData.model_name || !formData.endpoint_url) {
        showAlert('请填写所有必要字段', 'danger');
        return;
    }
    
    const url = currentSourceId ? 
        `/admin/api/ai-sources/${currentSourceId}` : 
        '/admin/api/ai-sources';
    const method = currentSourceId ? 'PUT' : 'POST';
    
    $.ajax({
        url: url,
        method: method,
        contentType: 'application/json',
        data: JSON.stringify(formData),
        success: function(response) {
            if (response.success) {
                showAlert(response.message, 'success');
                $('#aiSourceModal').modal('hide');
                refreshTable();
            } else {
                showAlert(response.message, 'danger');
            }
        },
        error: function(xhr) {
            let message = '操作失败';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                message = xhr.responseJSON.message;
            }
            showAlert(message, 'danger');
        }
    });
}

function deleteSource(sourceId) {
    if (!confirm('确定要删除这个AI源吗？此操作不可恢复。')) {
        return;
    }
    
    $.ajax({
        url: `/admin/api/ai-sources/${sourceId}`,
        method: 'DELETE',
        success: function(response) {
            if (response.success) {
                showAlert(response.message, 'success');
                refreshTable();
            } else {
                showAlert(response.message, 'danger');
            }
        },
        error: function() {
            showAlert('删除失败', 'danger');
        }
    });
}

function testConnection(sourceId) {
    const btn = event.target.closest('button');
    const originalHtml = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    btn.disabled = true;
    
    $.ajax({
        url: `/admin/api/ai-sources/${sourceId}/test`,
        method: 'POST',
        success: function(response) {
            if (response.success) {
                showAlert(response.message, 'success');
            } else {
                showAlert(response.message, 'warning');
            }
        },
        error: function() {
            showAlert('连接测试失败', 'danger');
        },
        complete: function() {
            btn.innerHTML = originalHtml;
            btn.disabled = false;
        }
    });
}

function refreshTable() {
    location.reload();
}

$(document).ready(function() {
    // 初始化页面
    console.log('AI源管理页面已加载');
});
</script>
{% endblock %}
