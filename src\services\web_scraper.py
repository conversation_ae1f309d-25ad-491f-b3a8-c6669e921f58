"""
网页抓取服务
"""
import requests
import time
from typing import Optional, List
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
from src.config import Config
from src.utils.logger import setup_logger

# 禁用SSL警告
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

logger = setup_logger(__name__)

class WebScraperService:
    """网页抓取服务类"""

    def __init__(self):
        """初始化网页抓取服务"""
        self.timeout = Config.SCRAPING_TIMEOUT
        self.max_retries = Config.MAX_RETRIES
        self.request_delay = Config.REQUEST_DELAY

        # 设置请求头，模拟真实浏览器
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }

        # # 创建session以支持自动解压缩
        # self.session = requests.Session()
        # self.session.headers.update(self.headers)

        # 延迟初始化DrissionPage服务（避免启动时就创建浏览器实例）
        self._drission_scraper = None

        logger.info("网页抓取服务初始化完成")

    def _get_drission_scraper(self):
        """
        获取DrissionPage抓取服务实例（延迟初始化）

        Returns:
            DrissionScraperService实例
        """
        if self._drission_scraper is None:
            try:
                from src.services.drission_scraper import DrissionScraperService
                self._drission_scraper = DrissionScraperService()
                logger.info("DrissionPage抓取服务已初始化")
            except Exception as e:
                logger.error(f"初始化DrissionPage抓取服务失败: {e}")
                raise
        return self._drission_scraper
    
    def fetch_page(self, url: str, encoding: str = 'utf-8') -> Optional[str]:
        """
        抓取网页内容
        使用三层重试策略：
        1. 第一次和第二次使用requests
        2. 第三次使用DrissionPage作为备用方案

        Args:
            url: 目标URL
            encoding: 页面编码

        Returns:
            网页HTML内容，失败返回None
        """
        logger.info(f"开始抓取页面: {url}")

        # 前两次使用requests重试
        requests_max_retries = min(2, self.max_retries)

        for attempt in range(requests_max_retries):
            try:
                logger.info(f"使用requests进行第 {attempt + 1} 次尝试: {url}")

                # 发送请求，使用session自动处理压缩
                response = requests.get(
                    url,
                    timeout=self.timeout,
                    headers=self.headers,
                    allow_redirects=True,
                    verify=False  # 暂时禁用SSL验证以处理某些网站的SSL问题
                )

                # 检查响应状态
                if response.status_code == 200:
                    # 尝试自动检测编码
                    response.encoding = 'utf-8'
                    content = response.text
                    # 清理HTML内容
                    content = self.clean_html_content(content)

                    logger.info(f"requests页面抓取成功: {url}, 内容长度: {len(content)}")
                    return content

                elif response.status_code in [301, 302, 303, 307, 308]:
                    # 处理重定向
                    redirect_url = response.headers.get('Location')
                    if redirect_url:
                        logger.info(f"页面重定向: {url} -> {redirect_url}")
                        return self.fetch_page(redirect_url, encoding)

                else:
                    logger.warning(f"requests页面抓取失败，状态码: {response.status_code}, URL: {url}")
                    if attempt < requests_max_retries - 1:
                        time.sleep(self.request_delay * (attempt + 1))
                        continue

            except requests.exceptions.Timeout:
                logger.warning(f"requests页面抓取超时，尝试 {attempt + 1}/{requests_max_retries}, URL: {url}")
                if attempt < requests_max_retries - 1:
                    time.sleep(self.request_delay * (attempt + 1))
                    continue

            except requests.exceptions.RequestException as e:
                logger.warning(f"requests页面抓取请求异常: {e}, URL: {url}")
                if attempt < requests_max_retries - 1:
                    time.sleep(self.request_delay * (attempt + 1))
                    continue

            except Exception as e:
                logger.warning(f"requests页面抓取过程中发生未知错误: {e}, URL: {url}")
                if attempt < requests_max_retries - 1:
                    time.sleep(self.request_delay * (attempt + 1))
                    continue

        # 如果requests重试失败，且配置允许更多重试，则使用DrissionPage
        if self.max_retries > requests_max_retries:
            logger.info(f"requests重试失败，尝试使用DrissionPage作为备用方案: {url}")
            try:
                drission_scraper = self._get_drission_scraper()
                content = drission_scraper.fetch_page(url)

                if content:
                    # 清理HTML内容
                    content = self.clean_html_content(content)
                    logger.info(f"DrissionPage页面抓取成功: {url}, 内容长度: {len(content)}")
                    return content
                else:
                    logger.warning(f"DrissionPage页面抓取失败: {url}")

            except Exception as e:
                logger.error(f"DrissionPage页面抓取异常: {e}, URL: {url}")

        logger.error(f"所有重试方案均失败: {url}")
        return None
    
    def fetch_page_with_soup(self, url: str) -> Optional[BeautifulSoup]:
        """
        抓取网页并返回BeautifulSoup对象
        
        Args:
            url: 目标URL
            
        Returns:
            BeautifulSoup对象，失败返回None
        """
        html_content = self.fetch_page(url)
        if html_content:
            try:
                soup = BeautifulSoup(html_content, 'html.parser')
                return soup
            except Exception as e:
                logger.error(f"HTML解析失败: {e}, URL: {url}")
                return None
        return None

    def fetch_page_with_js(self, url: str, wait_selector: str = None, wait_time: float = 5.0) -> Optional[str]:
        """
        专门抓取需要JavaScript渲染的页面
        直接使用DrissionPage，适用于已知需要JS渲染的页面

        Args:
            url: 目标URL
            wait_selector: 等待的CSS选择器
            wait_time: 最大等待时间（秒）

        Returns:
            网页HTML内容，失败返回None
        """
        logger.info(f"使用DrissionPage抓取JS渲染页面: {url}")

        try:
            drission_scraper = self._get_drission_scraper()
            content = drission_scraper.fetch_page_with_js_wait(url, wait_selector, wait_time)

            if content:
                # 清理HTML内容
                content = self.clean_html_content(content)
                logger.info(f"DrissionPage JS页面抓取成功: {url}, 内容长度: {len(content)}")
                return content
            else:
                logger.warning(f"DrissionPage JS页面抓取失败: {url}")
                return None

        except Exception as e:
            logger.error(f"DrissionPage JS页面抓取异常: {e}, URL: {url}")
            return None

    def extract_links(self, html_content: str, base_url: str) -> list:
        """
        从HTML内容中提取所有链接
        
        Args:
            html_content: HTML内容
            base_url: 基础URL，用于处理相对链接
            
        Returns:
            链接列表
        """
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            links = []
            
            for a_tag in soup.find_all('a', href=True):
                href = a_tag['href']
                text = a_tag.get_text(strip=True)
                
                # 处理相对链接
                if href.startswith('http'):
                    absolute_url = href
                else:
                    absolute_url = urljoin(base_url, href)
                
                links.append({
                    'url': absolute_url,
                    'text': text,
                    'href': href
                })
            
            logger.info(f"从页面提取到 {len(links)} 个链接")
            return links
            
        except Exception as e:
            logger.error(f"链接提取失败: {e}")
            return []
    
    def clean_html_content(self, html_content: str) -> str:
        """
        深度清理HTML内容，移除无关标签和脚本，大幅减少内容长度

        优化策略：
        1. 移除无关HTML标签（head、style、script等）
        2. 移除动态脚本内容和事件处理器
        3. 保留有效内容（文本、链接、结构化标签等）
        4. 清理属性和注释
        5. 压缩HTML结构

        Args:
            html_content: 原始HTML内容

        Returns:
            清理后的HTML内容
        """
        try:
            original_length = len(html_content)
            logger.info(f"开始HTML深度清理，原始长度: {original_length:,} 字符")

            soup = BeautifulSoup(html_content, 'html.parser')

            # 第一步：移除整个head标签及其所有内容
            head_tags = soup.find_all('head')
            for head_tag in head_tags:
                head_tag.decompose()
                logger.debug("移除head标签及其内容")

            # 第二步：移除无关的HTML标签
            unwanted_tags = [
                # 脚本和样式
                'script',      # JavaScript代码
                'style',       # CSS样式
                'link',        # 外部资源引用（CSS、图标等）
                'meta',        # 元数据标签
                'noscript',    # 无脚本内容

                # 媒体和嵌入内容
                'iframe',      # 内嵌框架
                'embed',       # 嵌入内容
                'object',      # 对象元素
                'svg',         # SVG图形
                'canvas',      # 画布元素
                'audio',       # 音频元素
                'video',       # 视频元素
                'source',      # 媒体源
                'track',       # 媒体轨道

                # 其他无关标签
                'base',        # 基础URL
                'title',       # 页面标题（通常在head中，但可能有遗漏）
                'template',    # 模板标签
                'slot',        # 插槽标签
            ]

            removed_count = 0
            for tag_name in unwanted_tags:
                tags = soup.find_all(tag_name)
                for tag in tags:
                    tag.decompose()
                    removed_count += 1

            logger.debug(f"移除了 {removed_count} 个无关标签")

            # 第三步：移除内联事件处理器属性和其他无用属性
            event_attributes = [
                # 鼠标事件
                'onclick', 'ondblclick', 'onmousedown', 'onmousemove', 'onmouseout',
                'onmouseover', 'onmouseup', 'onmouseenter', 'onmouseleave',
                # 键盘事件
                'onkeydown', 'onkeypress', 'onkeyup',
                # 表单事件
                'onchange', 'onsubmit', 'onreset', 'onselect', 'onblur', 'onfocus',
                'oninput', 'oninvalid',
                # 页面事件
                'onload', 'onunload', 'onbeforeunload', 'onresize', 'onscroll',
                # 拖拽事件
                'ondrag', 'ondragend', 'ondragenter', 'ondragleave', 'ondragover',
                'ondragstart', 'ondrop',
                # 媒体事件
                'onabort', 'oncanplay', 'oncanplaythrough', 'oncuechange', 'ondurationchange',
                'onemptied', 'onended', 'onerror', 'onloadeddata', 'onloadedmetadata',
                'onloadstart', 'onpause', 'onplay', 'onplaying', 'onprogress', 'onratechange',
                'onseeked', 'onseeking', 'onstalled', 'onsuspend', 'ontimeupdate',
                'onvolumechange', 'onwaiting',
                # 其他事件
                'onwheel', 'oncontextmenu', 'oncopy', 'oncut', 'onpaste'
            ]

            # 其他无用属性
            useless_attributes = [
                'style',           # 内联样式
                'role',            # 角色属性
                'tabindex',        # 标签索引
                'contenteditable', # 可编辑属性
                'draggable',       # 可拖拽属性
                'hidden',          # 隐藏属性
                'lang',            # 语言属性
                'dir',             # 文本方向
                'translate',       # 翻译属性
                'spellcheck',      # 拼写检查
                'autocomplete',    # 自动完成
                'autocorrect',     # 自动纠正
                'autocapitalize'   # 自动大写
            ]

            removed_attrs = 0
            for tag in soup.find_all():
                current_attrs = list(tag.attrs.keys())
                for attr in current_attrs:
                    # 移除事件处理器
                    if attr in event_attributes:
                        del tag[attr]
                        removed_attrs += 1
                    # 移除无用属性
                    elif attr in useless_attributes:
                        del tag[attr]
                        removed_attrs += 1
                    # 移除data-*属性（保留少数重要的）
                    elif attr.startswith('data-') and attr not in ['data-href', 'data-url', 'data-link']:
                        del tag[attr]
                        removed_attrs += 1
                    # 移除aria-*属性（保留少数重要的）
                    elif attr.startswith('aria-') and attr not in ['aria-label', 'aria-labelledby']:
                        del tag[attr]
                        removed_attrs += 1

            logger.debug(f"移除了 {removed_attrs} 个无用属性")

            # 第四步：清理属性，只保留重要的属性
            important_attributes = {
                'a': ['href', 'title', 'target'],
                'img': ['src', 'alt', 'title'],
                'form': ['action', 'method', 'name'],
                'input': ['type', 'name', 'value', 'placeholder'],
                'button': ['type', 'name', 'value'],
                'select': ['name', 'multiple'],
                'option': ['value', 'selected'],
                'textarea': ['name', 'placeholder'],
                'table': ['class', 'id'],
                'tr': ['class', 'id'],
                'td': ['class', 'id', 'colspan', 'rowspan'],
                'th': ['class', 'id', 'colspan', 'rowspan'],
                'div': ['class', 'id'],
                'span': ['class', 'id'],
                'p': ['class', 'id'],
                'h1': ['class', 'id'], 'h2': ['class', 'id'], 'h3': ['class', 'id'],
                'h4': ['class', 'id'], 'h5': ['class', 'id'], 'h6': ['class', 'id'],
                'ul': ['class', 'id'], 'ol': ['class', 'id'], 'li': ['class', 'id'],
                'nav': ['class', 'id'], 'section': ['class', 'id'], 'article': ['class', 'id'],
                'main': ['class', 'id'], 'header': ['class', 'id'], 'footer': ['class', 'id'],
                'aside': ['class', 'id'], 'figure': ['class', 'id'], 'figcaption': ['class', 'id']
            }

            cleaned_attrs = 0
            for tag in soup.find_all():
                if tag.name in important_attributes:
                    # 保留重要属性
                    attrs_to_keep = important_attributes[tag.name]
                    current_attrs = list(tag.attrs.keys())
                    for attr in current_attrs:
                        if attr not in attrs_to_keep:
                            del tag[attr]
                            cleaned_attrs += 1
                else:
                    # 对于其他标签，只保留class和id
                    universal_attrs = ['class', 'id']
                    current_attrs = list(tag.attrs.keys())
                    for attr in current_attrs:
                        if attr not in universal_attrs:
                            del tag[attr]
                            cleaned_attrs += 1

            logger.debug(f"清理了 {cleaned_attrs} 个非重要属性")

            # 第五步：移除HTML注释
            from bs4 import Comment
            comments_removed = 0
            for comment in soup.find_all(string=lambda text: isinstance(text, Comment)):
                comment.extract()
                comments_removed += 1
            logger.debug(f"移除了 {comments_removed} 个HTML注释")

            # 第六步：移除空白标签和无内容标签
            empty_tags_removed = 0
            # 多次遍历，因为移除标签可能会产生新的空标签
            for _ in range(3):
                for tag in soup.find_all():
                    # 检查是否为空标签（没有文本内容且没有子标签）
                    if (not tag.get_text(strip=True) and
                        not tag.find_all() and
                        tag.name not in ['br', 'hr', 'img', 'input', 'meta', 'link']):  # 保留自闭合标签
                        tag.decompose()
                        empty_tags_removed += 1

            logger.debug(f"移除了 {empty_tags_removed} 个空白标签")

            # 第七步：压缩HTML结构和空白字符
            cleaned_html = str(soup)

            # 使用正则表达式进行更精细的空白字符处理
            import re

            # 移除多余的空白字符，但保留必要的空格
            cleaned_html = re.sub(r'\n\s*\n', '\n', cleaned_html)  # 移除多余的空行
            cleaned_html = re.sub(r'\s+', ' ', cleaned_html)  # 多个空白字符替换为单个空格
            cleaned_html = re.sub(r'>\s+<', '><', cleaned_html)  # 移除标签间的空白
            cleaned_html = re.sub(r'^\s+|\s+$', '', cleaned_html)  # 移除首尾空白

            # 移除空的class和id属性
            cleaned_html = re.sub(r'\s+class=""', '', cleaned_html)
            cleaned_html = re.sub(r'\s+id=""', '', cleaned_html)

            cleaned_length = len(cleaned_html)
            reduction_percentage = ((original_length - cleaned_length) / original_length) * 100

            logger.info(f"HTML内容深度清理完成:")
            logger.info(f"  原始长度: {original_length:,} 字符")
            logger.info(f"  清理后长度: {cleaned_length:,} 字符")
            logger.info(f"  减少比例: {reduction_percentage:.1f}%")
            logger.info(f"  清理统计: 移除{removed_count}个标签, {removed_attrs}个属性, {comments_removed}个注释, {empty_tags_removed}个空标签")

            return cleaned_html

        except Exception as e:
            logger.error(f"HTML深度清理失败: {e}")
            return html_content

    def split_html_content(self, html_content: str, max_length: int = 50000) -> List[str]:
        """
        智能分割HTML内容，确保每个分片保持结构完整性

        优化策略：
        1. 优先按语义化的块级元素分割
        2. 保持HTML结构完整性
        3. 避免在重要内容中间分割
        4. 确保每个分片都有有效内容

        Args:
            html_content: 要分割的HTML内容
            max_length: 每个分片的最大长度（默认50000字符）

        Returns:
            HTML内容分片列表
        """
        try:
            if len(html_content) <= max_length:
                logger.debug(f"HTML内容长度({len(html_content):,}字符)未超过阈值，无需分割")
                return [html_content]

            logger.info(f"HTML内容过长({len(html_content):,}字符)，开始智能分割(阈值: {max_length:,})")

            soup = BeautifulSoup(html_content, 'html.parser')
            fragments = []

            # 优先按这些块级元素分割（按优先级排序）
            block_selectors = [
                # 高优先级：语义化容器
                'section',
                'article',
                'main',
                'aside',
                'header',
                'footer',
                # 中优先级：内容容器
                'div[class*="content"]',
                'div[class*="main"]',
                'div[class*="section"]',
                'div[class*="block"]',
                'div[class*="widget"]',
                'div[id*="content"]',
                'div[id*="main"]',
                'div[id*="section"]',
                # 低优先级：通用容器
                'div[class]',  # 有class的div
                'div[id]',     # 有id的div
                'div'          # 所有div
            ]

            # 尝试按块级元素分割
            for selector in block_selectors:
                try:
                    blocks = soup.select(selector)
                    if not blocks or len(blocks) <= 1:
                        continue

                    logger.debug(f"尝试使用选择器 '{selector}' (找到 {len(blocks)} 个元素)")

                    current_fragment = ""
                    fragment_count = 0

                    for i, block in enumerate(blocks):
                        block_html = str(block)

                        # 跳过过小的块（可能是无意义的内容）
                        if len(block_html.strip()) < 100:
                            continue

                        # 如果单个块就超过限制，需要进一步分割
                        if len(block_html) > max_length:
                            # 保存当前片段
                            if current_fragment.strip():
                                fragments.append(current_fragment)
                                fragment_count += 1
                                logger.debug(f"保存当前分片 {fragment_count}: {len(current_fragment):,} 字符")
                                current_fragment = ""

                            # 递归分割大块
                            sub_fragments = self._split_large_block(block_html, max_length)
                            fragments.extend(sub_fragments)
                            fragment_count += len(sub_fragments)
                            logger.debug(f"大块分割为 {len(sub_fragments)} 个子分片")
                            continue

                        # 如果当前片段加上新块会超过限制，保存当前片段
                        if current_fragment and len(current_fragment) + len(block_html) > max_length:
                            if current_fragment.strip():
                                fragments.append(current_fragment)
                                fragment_count += 1
                                logger.debug(f"创建分片 {fragment_count}: {len(current_fragment):,} 字符")
                            current_fragment = block_html
                        else:
                            current_fragment += block_html

                    # 添加最后一个片段
                    if current_fragment.strip():
                        fragments.append(current_fragment)
                        fragment_count += 1
                        logger.debug(f"创建最后分片 {fragment_count}: {len(current_fragment):,} 字符")

                    # 检查分割是否成功（至少有2个有效分片）
                    if len(fragments) >= 2:
                        logger.info(f"使用选择器 '{selector}' 成功分割为 {len(fragments)} 个分片")
                        break
                    else:
                        # 清空fragments，尝试下一个选择器
                        fragments = []

                except Exception as e:
                    logger.warning(f"使用选择器 '{selector}' 分割失败: {e}")
                    continue

            # 如果块级分割失败，使用字符长度强制分割
            if not fragments:
                logger.warning("所有块级分割策略失败，使用字符长度强制分割")
                fragments = self._force_split_by_length(html_content, max_length)

            # 验证分片质量
            fragments = self._validate_and_clean_fragments(fragments, max_length)

            logger.info(f"HTML内容分割完成，共生成 {len(fragments)} 个分片")
            for i, fragment in enumerate(fragments, 1):
                logger.debug(f"分片 {i}: {len(fragment):,} 字符")

            return fragments

        except Exception as e:
            logger.error(f"HTML内容分割失败: {e}")
            return [html_content]

    def _split_large_block(self, block_html: str, max_length: int) -> List[str]:
        """
        分割过大的HTML块

        Args:
            block_html: 要分割的HTML块
            max_length: 最大长度

        Returns:
            分割后的HTML片段列表
        """
        try:
            if len(block_html) <= max_length:
                return [block_html]

            logger.debug(f"分割大块HTML({len(block_html):,}字符)")

            soup = BeautifulSoup(block_html, 'html.parser')
            fragments = []

            # 尝试按子元素分割
            if soup.find():
                current_fragment = ""
                for child in soup.children:
                    if hasattr(child, 'name'):  # 是标签元素
                        child_html = str(child)
                        if len(child_html) > max_length:
                            # 递归分割
                            if current_fragment.strip():
                                fragments.append(current_fragment)
                                current_fragment = ""
                            sub_fragments = self._split_large_block(child_html, max_length)
                            fragments.extend(sub_fragments)
                        elif current_fragment and len(current_fragment) + len(child_html) > max_length:
                            fragments.append(current_fragment)
                            current_fragment = child_html
                        else:
                            current_fragment += child_html
                    else:  # 是文本节点
                        text = str(child).strip()
                        if text:
                            if current_fragment and len(current_fragment) + len(text) > max_length:
                                fragments.append(current_fragment)
                                current_fragment = text
                            else:
                                current_fragment += text

                if current_fragment.strip():
                    fragments.append(current_fragment)

            # 如果子元素分割失败，使用强制分割
            if not fragments:
                fragments = self._force_split_by_length(block_html, max_length)

            return fragments

        except Exception as e:
            logger.warning(f"大块分割失败: {e}")
            return self._force_split_by_length(block_html, max_length)

    def _force_split_by_length(self, content: str, max_length: int) -> List[str]:
        """
        按字符长度强制分割内容

        Args:
            content: 要分割的内容
            max_length: 最大长度

        Returns:
            分割后的内容列表
        """
        fragments = []
        remaining_content = content
        fragment_count = 0

        while remaining_content:
            if len(remaining_content) <= max_length:
                fragments.append(remaining_content)
                fragment_count += 1
                break

            # 寻找合适的分割点（尽量在标签边界或空白处）
            split_point = max_length

            # 优先在标签结束处分割
            for i in range(max_length - 200, max_length):
                if i < len(remaining_content):
                    if remaining_content[i] == '>':
                        split_point = i + 1
                        break
                    elif remaining_content[i] in [' ', '\n', '\t']:
                        split_point = i + 1

            fragment = remaining_content[:split_point]
            fragments.append(fragment)
            fragment_count += 1
            logger.debug(f"强制分片 {fragment_count}: {len(fragment):,} 字符")

            remaining_content = remaining_content[split_point:]

        return fragments

    def _validate_and_clean_fragments(self, fragments: List[str], max_length: int) -> List[str]:
        """
        验证和清理分片

        Args:
            fragments: 原始分片列表
            max_length: 最大长度

        Returns:
            清理后的分片列表
        """
        cleaned_fragments = []

        for i, fragment in enumerate(fragments):
            # 移除空白分片
            if not fragment.strip():
                logger.debug(f"跳过空白分片 {i+1}")
                continue

            # 检查分片长度
            if len(fragment) > max_length * 1.1:  # 允许10%的超出
                logger.warning(f"分片 {i+1} 仍然过长({len(fragment):,}字符)，进行二次分割")
                sub_fragments = self._force_split_by_length(fragment, max_length)
                cleaned_fragments.extend(sub_fragments)
            else:
                cleaned_fragments.append(fragment)

        logger.debug(f"分片验证完成: {len(fragments)} -> {len(cleaned_fragments)}")
        return cleaned_fragments

    def is_valid_url(self, url: str) -> bool:
        """
        检查URL是否有效
        
        Args:
            url: 要检查的URL
            
        Returns:
            URL是否有效
        """
        try:
            result = urlparse(url)
            return all([result.scheme, result.netloc])
        except Exception:
            return False

    def close(self):
        """关闭所有资源"""
        if self._drission_scraper:
            try:
                self._drission_scraper.close()
                self._drission_scraper = None
                logger.info("DrissionPage资源已清理")
            except Exception as e:
                logger.warning(f"清理DrissionPage资源时出现警告: {e}")

    def __del__(self):
        """析构函数，确保资源被清理"""
        self.close()
