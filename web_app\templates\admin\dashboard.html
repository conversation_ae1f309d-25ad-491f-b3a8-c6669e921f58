{% extends "base.html" %}

{% block title %}管理员仪表板 - AI工具新闻查找平台{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-tachometer-alt me-2"></i>管理员仪表板
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshStats()">
                <i class="fas fa-sync-alt me-1"></i>刷新数据
            </button>
        </div>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            用户总数
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ user_stats.total_users or 0 }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            活跃用户
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ user_stats.active_users or 0 }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-user-check fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            AI源数量
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ ai_source_stats.enabled }}/{{ ai_source_stats.total }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-robot fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            数据源数量
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ data_source_stats.enabled }}/{{ data_source_stats.total }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-database fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 快速操作 -->
<div class="row mb-4">
    <div class="col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-cogs me-2"></i>快速操作
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('admin.ai_sources') }}" class="btn btn-outline-primary">
                        <i class="fas fa-robot me-2"></i>管理AI源
                    </a>
                    <a href="{{ url_for('admin.data_sources') }}" class="btn btn-outline-info">
                        <i class="fas fa-database me-2"></i>管理数据源
                    </a>
                    <a href="{{ url_for('admin.users') }}" class="btn btn-outline-success">
                        <i class="fas fa-users me-2"></i>管理用户
                    </a>
                    <a href="{{ url_for('admin.system_logs') }}" class="btn btn-outline-warning">
                        <i class="fas fa-file-alt me-2"></i>查看系统日志
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-info-circle me-2"></i>系统信息
                </h6>
            </div>
            <div class="card-body">
                <div class="small text-muted">
                    <p><strong>平台版本:</strong> v1.0.0</p>
                    <p><strong>当前用户:</strong> {{ current_user.username }} (管理员)</p>
                    <p><strong>登录时间:</strong> {{ current_user.last_login.strftime('%Y-%m-%d %H:%M:%S') if current_user.last_login else '首次登录' }}</p>
                    <p><strong>系统状态:</strong> <span class="text-success">正常运行</span></p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 最近活动 -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">
            <i class="fas fa-clock me-2"></i>最近活动
        </h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered" id="activityTable" width="100%" cellspacing="0">
                <thead>
                    <tr>
                        <th>时间</th>
                        <th>用户</th>
                        <th>操作</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>{{ moment().format('YYYY-MM-DD HH:mm:ss') }}</td>
                        <td>{{ current_user.username }}</td>
                        <td>登录系统</td>
                        <td><span class="badge bg-success">成功</span></td>
                    </tr>
                    <tr>
                        <td colspan="4" class="text-center text-muted">暂无更多活动记录</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
.text-gray-300 {
    color: #dddfeb !important;
}
.text-gray-800 {
    color: #5a5c69 !important;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function refreshStats() {
    // 刷新统计数据
    location.reload();
}

$(document).ready(function() {
    // 初始化页面
    console.log('管理员仪表板已加载');
});
</script>
{% endblock %}
