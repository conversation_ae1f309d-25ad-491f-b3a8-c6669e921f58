# 新闻板块发掘结果结构

## 概述

本文档描述了扩展后的新闻板块发掘功能的结果数据结构。新版本不仅能发现投资者关系板块，还能发掘一般新闻板块，并对XPath规则进行分类管理。

## 新的结果结构

### 完整结果示例

```json
{
  "company_name": "示例公司",
  "base_url": "https://example.com/",
  "research_timestamp": 1751451032.5199273,
  "status": "completed",
  
  // 新增：分类的新闻板块URL
  "investor_relations_urls": [
    "https://example.com/investors",
    "https://example.com/ir/financials",
    "https://example.com/sec-filings"
  ],
  "news_section_urls": [
    "https://example.com/news",
    "https://example.com/press-releases",
    "https://example.com/media-center"
  ],
  "mixed_section_urls": [
    "https://example.com/news-and-events",
    "https://example.com/investor-news"
  ],
  
  // 新增：分类的XPath规则
  "investor_xpath_rules": [
    "//div[@class='investor-news']//a[@href]",
    "//section[@id='financial-reports']//li//a",
    "//table[@class='sec-filings']//td//a[@href]"
  ],
  "news_xpath_rules": [
    "//div[@class='company-news']//a[@href]",
    "//ul[@id='press-releases']//li//a",
    "//article[@class='news-item']//h2//a"
  ],
  "general_xpath_rules": [
    "//div[@class='news-list']//a[@href]",
    "//section[@class='content-list']//a[@href]"
  ],
  
  // 向后兼容：保留原有字段
  "all_xpath_rules": [
    "//div[@class='investor-news']//a[@href]",
    "//div[@class='company-news']//a[@href]",
    "//div[@class='news-list']//a[@href]"
  ]
}
```

## 字段说明

### 新增字段

#### 新闻板块分类
- **`news_section_urls`**: 一般新闻板块URL列表
  - 包含公司新闻、产品发布、媒体报道等页面
  - 面向公众和客户的新闻信息

- **`mixed_section_urls`**: 混合板块URL列表
  - 同时包含投资者信息和一般新闻的页面
  - 综合性的新闻中心或信息发布平台

#### XPath规则分类
- **`investor_xpath_rules`**: 投资者关系XPath规则
  - 专门用于提取投资者相关内容
  - 如财务报告、SEC文件、股东会议等

- **`news_xpath_rules`**: 新闻内容XPath规则
  - 专门用于提取一般新闻内容
  - 如公司新闻、产品发布、媒体报道等

- **`general_xpath_rules`**: 通用XPath规则
  - 可以同时提取多种类型内容
  - 适用于混合页面或结构相似的内容

### 保留字段（向后兼容）

- **`investor_relations_urls`**: 投资者关系页面URL列表（保持原有功能）
- **`all_xpath_rules`**: 所有XPath规则的合并列表（向后兼容）

## 板块分类逻辑

### 投资者关系板块
**关键词识别**：
- 中文：投资者关系、投资者、财务信息、年报、季报
- 英文：investor, IR, financial, SEC, earnings, quarterly, annual

**内容特征**：
- 财务报告和文件
- SEC文件和合规信息
- 股东和治理信息
- 股价和股息信息

### 一般新闻板块
**关键词识别**：
- 中文：新闻、媒体、公告、动态、发布
- 英文：news, press, media, announcement, blog, updates

**内容特征**：
- 公司新闻和动态
- 产品发布和更新
- 行业新闻和趋势
- 媒体报道和采访

### 混合板块
**识别标准**：
- 同时包含投资者和新闻关键词
- URL路径显示综合性质
- 页面内容涵盖多种类型

## 使用示例

### 基本使用
```python
from src.core.search_research import SearchResearchClass

research = SearchResearchClass()
result = research.research_company("公司名称")

# 访问分类的新闻板块
investor_urls = result.get('investor_relations_urls', [])
news_urls = result.get('news_section_urls', [])
mixed_urls = result.get('mixed_section_urls', [])

# 访问分类的XPath规则
investor_xpath = result.get('investor_xpath_rules', [])
news_xpath = result.get('news_xpath_rules', [])
general_xpath = result.get('general_xpath_rules', [])

print(f"投资者关系页面: {len(investor_urls)}个")
print(f"一般新闻页面: {len(news_urls)}个")
print(f"混合板块页面: {len(mixed_urls)}个")
```

### 向后兼容使用
```python
# 原有代码仍然可以正常工作
result = research.research_company("公司名称")
all_investor_urls = result.get('investor_relations_urls', [])
all_xpath_rules = result.get('all_xpath_rules', [])
```

## 优势和改进

### 1. 更全面的信息发掘
- **扩大覆盖范围**：不仅限于投资者关系，还包括一般新闻
- **分类管理**：不同类型的内容分别管理，便于针对性处理
- **结构化组织**：清晰的分类结构，便于理解和使用

### 2. 更精确的XPath规则
- **分类提取**：根据内容类型提供专门的XPath规则
- **提高准确性**：针对性的规则提高提取准确性
- **减少冲突**：避免不同类型内容的XPath规则冲突

### 3. 更好的向后兼容性
- **保留原有字段**：确保现有代码不受影响
- **渐进式升级**：可以逐步迁移到新的结构
- **灵活使用**：支持新旧两种使用方式

## 迁移指南

### 从旧版本迁移

1. **检查新字段**：
```python
# 检查是否有新的分类字段
if 'news_section_urls' in result:
    # 使用新的分类结构
    news_urls = result['news_section_urls']
else:
    # 回退到旧的结构
    news_urls = []
```

2. **使用分类XPath**：
```python
# 优先使用分类的XPath规则
investor_xpath = result.get('investor_xpath_rules', [])
news_xpath = result.get('news_xpath_rules', [])

# 如果没有分类规则，使用合并的规则
if not investor_xpath and not news_xpath:
    all_xpath = result.get('all_xpath_rules', [])
```

3. **处理混合板块**：
```python
# 新增对混合板块的处理
mixed_urls = result.get('mixed_section_urls', [])
if mixed_urls:
    # 混合板块可能需要特殊处理
    for url in mixed_urls:
        # 根据具体内容进一步分析
        pass
```

## 注意事项

1. **API兼容性**：新版本完全向后兼容，现有代码无需修改
2. **性能影响**：新功能可能增加分析时间，但提供更全面的结果
3. **配置要求**：确保OpenAI API配置正确，新功能需要更多AI分析
4. **错误处理**：新功能包含回退机制，分析失败时自动使用原有方法

## 总结

新的新闻板块发掘功能显著扩展了系统的能力，不仅能发现投资者关系信息，还能全面发掘公司的新闻和媒体内容。通过分类管理和结构化组织，为用户提供更精确、更全面的信息提取能力。
