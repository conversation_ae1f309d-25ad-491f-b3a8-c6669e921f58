{% extends "base.html" %}

{% block title %}用户仪表板 - AI工具新闻查找平台{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-home me-2"></i>用户仪表板
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('user.search') }}" class="btn btn-sm btn-primary">
                <i class="fas fa-search me-1"></i>开始搜索
            </a>
        </div>
    </div>
</div>

<!-- 欢迎信息 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card bg-primary text-white shadow">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="h5 mb-0 font-weight-bold">
                            欢迎使用AI工具新闻查找平台，{{ current_user.username }}！
                        </div>
                        <div class="mt-2">
                            基于AI驱动的公司调研工具，帮助您快速获取准确的企业信息和新闻动态。
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-user-circle fa-3x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 可用资源统计 -->
<div class="row mb-4">
    <div class="col-xl-6 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            可用AI源
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ ai_sources|length }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-robot fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-6 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            可用数据源
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ data_sources|length }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-database fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 快速操作 -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-rocket me-2"></i>快速开始
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="card border-left-primary h-100">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-search me-2"></i>单次搜索
                                </h6>
                                <p class="card-text small">
                                    选择AI源和数据源，输入公司名称进行一次性搜索调研。
                                </p>
                                <a href="{{ url_for('user.search') }}" class="btn btn-primary btn-sm">
                                    开始搜索
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card border-left-success h-100">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-history me-2"></i>搜索历史
                                </h6>
                                <p class="card-text small">
                                    查看之前的搜索记录和结果，支持结果导出。
                                </p>
                                <a href="{{ url_for('user.history') }}" class="btn btn-success btn-sm">
                                    查看历史
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-info-circle me-2"></i>使用说明
                </h6>
            </div>
            <div class="card-body">
                <div class="small">
                    <ol>
                        <li>选择合适的AI源和数据源</li>
                        <li>输入要调研的公司名称</li>
                        <li>点击开始搜索</li>
                        <li>实时查看搜索进度</li>
                        <li>获取结构化的调研结果</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 可用资源列表 -->
<div class="row">
    <div class="col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-robot me-2"></i>可用AI源
                </h6>
            </div>
            <div class="card-body">
                {% if ai_sources %}
                    <div class="list-group list-group-flush">
                        {% for source in ai_sources %}
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">{{ source.name }}</h6>
                                <small class="text-muted">{{ source.model_name }}</small>
                            </div>
                            <span class="badge bg-success rounded-pill">可用</span>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center text-muted">
                        <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                        <p>暂无可用的AI源</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-database me-2"></i>可用数据源
                </h6>
            </div>
            <div class="card-body">
                {% if data_sources %}
                    <div class="list-group list-group-flush">
                        {% for source in data_sources %}
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">{{ source.name }}</h6>
                                <small class="text-muted">{{ source.source_type }}</small>
                            </div>
                            <span class="badge bg-success rounded-pill">可用</span>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center text-muted">
                        <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                        <p>暂无可用的数据源</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.text-gray-300 {
    color: #dddfeb !important;
}
.text-gray-800 {
    color: #5a5c69 !important;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 初始化用户仪表板
    console.log('用户仪表板已加载');
    
    // 检查是否有可用的AI源和数据源
    const aiSourceCount = {{ ai_sources|length }};
    const dataSourceCount = {{ data_sources|length }};
    
    if (aiSourceCount === 0 || dataSourceCount === 0) {
        showAlert('部分资源不可用，请联系管理员配置AI源和数据源', 'warning');
    }
});
</script>
{% endblock %}
