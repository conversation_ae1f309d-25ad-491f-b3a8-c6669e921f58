"""
Flask Web应用主文件
"""
import os
import sys
from flask import <PERSON>lask, render_template, redirect, url_for
from flask_login import <PERSON><PERSON>Mana<PERSON>, current_user

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from config.web_config import config
from web_app.models import db
from web_app.models.user import User


def create_app(config_name=None):
    """创建Flask应用"""
    if config_name is None:
        config_name = os.getenv('FLASK_CONFIG', 'development')
    
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    config[config_name].init_app(app)
    
    # 初始化扩展
    db.init_app(app)
    
    # 初始化登录管理器
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'
    login_manager.login_message = '请先登录'
    login_manager.login_message_category = 'warning'
    
    @login_manager.user_loader
    def load_user(user_id):
        return User.query.get(int(user_id))
    
    # 注册蓝图
    from web_app.routes.auth import auth_bp
    from web_app.routes.admin import admin_bp
    from web_app.routes.user import user_bp

    app.register_blueprint(auth_bp)
    app.register_blueprint(admin_bp)
    app.register_blueprint(user_bp)
    
    # 主页路由
    @app.route('/')
    def index():
        """主页"""
        if current_user.is_authenticated:
            if current_user.is_admin():
                return redirect(url_for('admin.dashboard'))
            else:
                return redirect(url_for('user.dashboard'))
        return redirect(url_for('auth.login'))
    
    # 错误处理
    @app.errorhandler(404)
    def not_found_error(error):
        return render_template('errors/404.html'), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        db.session.rollback()
        return render_template('errors/500.html'), 500
    
    @app.errorhandler(403)
    def forbidden_error(error):
        return render_template('errors/403.html'), 403
    
    # 上下文处理器
    @app.context_processor
    def inject_user():
        return dict(current_user=current_user)
    
    return app


if __name__ == '__main__':
    app = create_app()
    
    # 确保数据库表存在
    with app.app_context():
        db.create_all()
        
        # 创建测试账号（如果不存在）
        User.create_test_accounts()
    
    app.run(debug=True, host='0.0.0.0', port=5000)
