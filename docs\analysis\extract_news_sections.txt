你是一个专业的网页结构分析专家。请分析以下HTML内容，找出所有可能的新闻板块页面链接，并将其分类为投资者关系板块、一般新闻板块和混合板块。

公司名称：{company_name}
网站首页URL：{base_url}
HTML内容：
{html_content}

请按照以下要求分析：

1. 板块分类标准：

   **投资者关系板块**：
   - 包含"投资者关系"、"投资者"、"IR"、"Investor Relations"等关键词
   - 包含"财务信息"、"年报"、"季报"、"SEC文件"、"股东"、"治理"等词汇
   - 专门面向投资者和股东的信息板块
   - 示例：/investors, /ir, /financials, /sec-filings, /shareholder

   **一般新闻板块**：
   - 包含"新闻"、"News"、"Press"、"媒体"、"公告"、"动态"等关键词
   - 包含"产品发布"、"公司动态"、"行业新闻"、"媒体报道"等内容
   - 面向公众和客户的新闻信息
   - 示例：/news, /press, /media, /newsroom, /announcements, /blog

   **混合板块**：
   - 同时包含投资者信息和一般新闻的板块
   - 可能是综合性的新闻中心或信息发布平台
   - 示例：/news-and-events, /press-and-investor, /media-center

2. 识别策略：
   - 分析导航菜单、页脚链接、侧边栏和主要内容区域
   - 注意中英文表述的差异
   - 考虑URL路径和页面标题的语义
   - 识别页面层级结构和分类逻辑

3. 链接处理：
   - 将相对链接转换为绝对链接
   - 确保链接格式正确
   - 去除重复链接
   - 验证链接的有效性

4. 常见位置：
   - 主导航菜单
   - 页脚导航
   - 侧边栏链接
   - 主要内容区域的快速链接
   - 面包屑导航

5. 输出要求：
   请以JSON格式返回结果：
   ```json
   {
     "investor_relations_urls": [
       "https://example.com/investors",
       "https://example.com/ir/financials",
       "https://example.com/sec-filings"
     ],
     "news_section_urls": [
       "https://example.com/news",
       "https://example.com/press-releases",
       "https://example.com/media-center"
     ],
     "mixed_section_urls": [
       "https://example.com/news-and-events",
       "https://example.com/investor-news"
     ],
     "found_keywords": {
       "investor": ["投资者关系", "IR", "财务信息"],
       "news": ["新闻", "Press", "媒体中心"],
       "mixed": ["新闻与活动", "投资者新闻"]
     },
     "confidence": 0.9,
     "classification_notes": "分类过程的详细说明",
     "structure_analysis": "页面结构分析"
   }
   ```

6. 分类原则：
   - **优先级**：如果一个链接同时符合多个分类，按以下优先级分类：
     1. 如果明确标注为投资者专用，归类为投资者关系
     2. 如果明确标注为新闻媒体，归类为一般新闻
     3. 如果包含两种类型内容，归类为混合板块
   
   - **上下文分析**：考虑链接在页面中的位置和上下文
   - **URL语义**：分析URL路径的语义含义
   - **标题分析**：分析链接文本和页面标题的含义

注意：
- 如果某个分类没有找到相关链接，返回空数组
- confidence表示整体分类的置信度
- classification_notes要详细说明分类的依据和过程
- structure_analysis要描述页面的整体结构和导航逻辑
- 确保每个URL只出现在一个分类中，避免重复
