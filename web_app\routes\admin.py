"""
管理员路由
"""
from flask import Blueprint, render_template, request, jsonify, redirect, url_for, flash
from flask_login import current_user
from web_app.models.user import User
from web_app.models.ai_source import AISource
from web_app.models.data_source import DataSource
from web_app.services.auth_service import AuthService
from web_app.utils.decorators import admin_required, json_required, validate_json_fields, handle_errors
from web_app.models import db

admin_bp = Blueprint('admin', __name__, url_prefix='/admin')


@admin_bp.route('/dashboard')
@admin_required
def dashboard():
    """管理员仪表板"""
    # 获取统计信息
    user_stats = AuthService.get_user_stats()
    ai_source_stats = {
        'total': AISource.query.count(),
        'enabled': AISource.query.filter_by(enabled=True).count()
    }
    data_source_stats = {
        'total': DataSource.query.count(),
        'enabled': DataSource.query.filter_by(enabled=True).count()
    }
    
    return render_template('admin/dashboard.html', 
                         user_stats=user_stats,
                         ai_source_stats=ai_source_stats,
                         data_source_stats=data_source_stats)


@admin_bp.route('/ai-sources')
@admin_required
def ai_sources():
    """AI源管理页面"""
    sources = AISource.query.all()
    return render_template('admin/ai_sources.html', sources=sources)


@admin_bp.route('/api/ai-sources', methods=['GET', 'POST'])
@admin_required
def api_ai_sources():
    """AI源API"""
    if request.method == 'GET':
        sources = AISource.query.all()
        return jsonify({
            'success': True,
            'data': [source.to_dict() for source in sources]
        })
    
    elif request.method == 'POST':
        if not request.is_json:
            return jsonify({'success': False, 'message': '需要JSON格式'}), 400
        
        data = request.get_json()
        required_fields = ['name', 'endpoint_url', 'model_name']
        
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    'success': False,
                    'message': f'缺少必要字段: {field}'
                }), 400
        
        try:
            # 检查名称是否已存在
            if AISource.get_by_name(data['name']):
                return jsonify({
                    'success': False,
                    'message': 'AI源名称已存在'
                }), 400
            
            # 创建新AI源
            source = AISource(
                name=data['name'],
                endpoint_url=data['endpoint_url'],
                model_name=data['model_name'],
                api_key=data.get('api_key', ''),
                enabled=data.get('enabled', True),
                config_params=data.get('config_params', {}),
                description=data.get('description', '')
            )
            source.save()
            
            return jsonify({
                'success': True,
                'message': 'AI源创建成功',
                'data': source.to_dict()
            })
            
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'创建失败: {str(e)}'
            }), 500


@admin_bp.route('/api/ai-sources/<int:source_id>', methods=['GET', 'PUT', 'DELETE'])
@admin_required
def api_ai_source_detail(source_id):
    """AI源详情API"""
    source = AISource.query.get_or_404(source_id)
    
    if request.method == 'GET':
        return jsonify({
            'success': True,
            'data': source.to_dict(include_sensitive=True)
        })
    
    elif request.method == 'PUT':
        if not request.is_json:
            return jsonify({'success': False, 'message': '需要JSON格式'}), 400
        
        data = request.get_json()
        
        try:
            # 更新字段
            if 'name' in data:
                # 检查名称是否与其他AI源冲突
                existing = AISource.get_by_name(data['name'])
                if existing and existing.id != source.id:
                    return jsonify({
                        'success': False,
                        'message': 'AI源名称已存在'
                    }), 400
                source.name = data['name']
            
            if 'endpoint_url' in data:
                source.endpoint_url = data['endpoint_url']
            if 'model_name' in data:
                source.model_name = data['model_name']
            if 'api_key' in data:
                source.api_key = data['api_key']
            if 'enabled' in data:
                source.enabled = data['enabled']
            if 'config_params' in data:
                source.set_config_params(data['config_params'])
            if 'description' in data:
                source.description = data['description']
            
            db.session.commit()
            
            return jsonify({
                'success': True,
                'message': 'AI源更新成功',
                'data': source.to_dict()
            })
            
        except Exception as e:
            db.session.rollback()
            return jsonify({
                'success': False,
                'message': f'更新失败: {str(e)}'
            }), 500
    
    elif request.method == 'DELETE':
        try:
            source.delete()
            return jsonify({
                'success': True,
                'message': 'AI源删除成功'
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'删除失败: {str(e)}'
            }), 500


@admin_bp.route('/api/ai-sources/<int:source_id>/test', methods=['POST'])
@admin_required
def test_ai_source(source_id):
    """测试AI源连接"""
    source = AISource.query.get_or_404(source_id)
    
    try:
        success, message = source.test_connection()
        return jsonify({
            'success': success,
            'message': message
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'测试失败: {str(e)}'
        }), 500


@admin_bp.route('/data-sources')
@admin_required
def data_sources():
    """数据源管理页面"""
    sources = DataSource.query.all()
    return render_template('admin/data_sources.html', sources=sources)


@admin_bp.route('/api/data-sources', methods=['GET', 'POST'])
@admin_required
def api_data_sources():
    """数据源API"""
    if request.method == 'GET':
        sources = DataSource.query.all()
        return jsonify({
            'success': True,
            'data': [source.to_dict() for source in sources]
        })

    elif request.method == 'POST':
        if not request.is_json:
            return jsonify({'success': False, 'message': '需要JSON格式'}), 400

        data = request.get_json()
        required_fields = ['name', 'source_type']

        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    'success': False,
                    'message': f'缺少必要字段: {field}'
                }), 400

        try:
            # 检查名称是否已存在
            if DataSource.get_by_name(data['name']):
                return jsonify({
                    'success': False,
                    'message': '数据源名称已存在'
                }), 400

            # 创建新数据源
            source = DataSource(
                name=data['name'],
                source_type=data['source_type'],
                enabled=data.get('enabled', True),
                config_params=data.get('config_params', {}),
                description=data.get('description', '')
            )
            source.save()

            return jsonify({
                'success': True,
                'message': '数据源创建成功',
                'data': source.to_dict()
            })

        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'创建失败: {str(e)}'
            }), 500


@admin_bp.route('/api/data-sources/<int:source_id>', methods=['GET', 'PUT', 'DELETE'])
@admin_required
def api_data_source_detail(source_id):
    """数据源详情API"""
    source = DataSource.query.get_or_404(source_id)

    if request.method == 'GET':
        return jsonify({
            'success': True,
            'data': source.to_dict(include_sensitive=True)
        })

    elif request.method == 'PUT':
        if not request.is_json:
            return jsonify({'success': False, 'message': '需要JSON格式'}), 400

        data = request.get_json()

        try:
            # 更新字段
            if 'name' in data:
                # 检查名称是否与其他数据源冲突
                existing = DataSource.get_by_name(data['name'])
                if existing and existing.id != source.id:
                    return jsonify({
                        'success': False,
                        'message': '数据源名称已存在'
                    }), 400
                source.name = data['name']

            if 'source_type' in data:
                source.source_type = data['source_type']
            if 'enabled' in data:
                source.enabled = data['enabled']
            if 'config_params' in data:
                source.set_config_params(data['config_params'])
            if 'description' in data:
                source.description = data['description']

            db.session.commit()

            return jsonify({
                'success': True,
                'message': '数据源更新成功',
                'data': source.to_dict()
            })

        except Exception as e:
            db.session.rollback()
            return jsonify({
                'success': False,
                'message': f'更新失败: {str(e)}'
            }), 500

    elif request.method == 'DELETE':
        try:
            source.delete()
            return jsonify({
                'success': True,
                'message': '数据源删除成功'
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'删除失败: {str(e)}'
            }), 500


@admin_bp.route('/api/data-sources/<int:source_id>/test', methods=['POST'])
@admin_required
def test_data_source(source_id):
    """测试数据源连接"""
    source = DataSource.query.get_or_404(source_id)

    try:
        success, message = source.test_connection()
        return jsonify({
            'success': success,
            'message': message
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'测试失败: {str(e)}'
        }), 500


@admin_bp.route('/users')
@admin_required
def users():
    """用户管理页面"""
    users = User.query.all()
    return render_template('admin/users.html', users=users)


@admin_bp.route('/system-logs')
@admin_required
def system_logs():
    """系统日志页面"""
    return render_template('admin/system_logs.html')
