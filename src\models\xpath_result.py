"""
XPath结果数据模型
用于维护xpath选择器与URL的绑定关系
"""

import time
from typing import List, Dict, Any, Optional
from dataclasses import dataclass


@dataclass
class XPathRuleItem:
    """单个XPath规则项，包含规则和来源信息"""
    xpath: str
    source_url: str
    page_type: str  # investor_relations, news_section, mixed_section
    confidence: float = 0.0
    created_at: float = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = time.time()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'xpath': self.xpath,
            'source_url': self.source_url,
            'page_type': self.page_type,
            'confidence': self.confidence,
            'created_at': self.created_at
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'XPathRuleItem':
        """从字典创建实例"""
        return cls(
            xpath=data['xpath'],
            source_url=data['source_url'],
            page_type=data['page_type'],
            confidence=data.get('confidence', 0.0),
            created_at=data.get('created_at')
        )


class ClassifiedXPathResult:
    """分类的XPath结果，维护xpath与URL的绑定关系"""
    
    def __init__(self):
        self.investor_xpath_rules: List[XPathRuleItem] = []
        self.news_xpath_rules: List[XPathRuleItem] = []
        self.general_xpath_rules: List[XPathRuleItem] = []
    
    def add_rules(self, xpath_list: List[str], source_url: str, page_type: str, 
                  rule_type: str, confidence: float = 0.0) -> None:
        """
        添加xpath规则并维护绑定关系
        
        Args:
            xpath_list: xpath规则列表
            source_url: 来源URL
            page_type: 页面类型
            rule_type: 规则类型 (investor, news, general)
            confidence: 置信度
        """
        if rule_type not in ['investor', 'news', 'general']:
            raise ValueError(f"Invalid rule_type: {rule_type}")
        
        target_list = getattr(self, f"{rule_type}_xpath_rules")
        for xpath in xpath_list:
            if xpath:  # 过滤空字符串
                rule_item = XPathRuleItem(xpath, source_url, page_type, confidence)
                target_list.append(rule_item)
    
    def get_rules_by_url(self, url: str) -> Dict[str, List[str]]:
        """
        根据URL获取对应的xpath规则
        
        Args:
            url: 目标URL
            
        Returns:
            包含三种类型xpath规则的字典
        """
        result = {
            'investor_xpath_rules': [],
            'news_xpath_rules': [],
            'general_xpath_rules': []
        }
        
        for rule_type in ['investor', 'news', 'general']:
            rules = getattr(self, f"{rule_type}_xpath_rules")
            result[f"{rule_type}_xpath_rules"] = [
                rule.xpath for rule in rules if rule.source_url == url
            ]
        
        return result
    
    def get_rules_by_type(self, rule_type: str) -> List[XPathRuleItem]:
        """
        根据类型获取xpath规则项
        
        Args:
            rule_type: 规则类型 (investor, news, general)
            
        Returns:
            XPathRuleItem列表
        """
        if rule_type not in ['investor', 'news', 'general']:
            raise ValueError(f"Invalid rule_type: {rule_type}")
        
        return getattr(self, f"{rule_type}_xpath_rules")
    
    def get_all_rules_with_sources(self) -> Dict[str, List[Dict]]:
        """
        获取所有规则及其来源信息
        
        Returns:
            包含详细来源信息的规则字典
        """
        return {
            'investor_xpath_rules': [rule.to_dict() for rule in self.investor_xpath_rules],
            'news_xpath_rules': [rule.to_dict() for rule in self.news_xpath_rules],
            'general_xpath_rules': [rule.to_dict() for rule in self.general_xpath_rules]
        }
    
    def get_legacy_format(self) -> Dict[str, List[str]]:
        """
        获取向后兼容的格式（仅xpath字符串列表）
        
        Returns:
            传统格式的xpath规则字典
        """
        return {
            'investor_xpath_rules': [rule.xpath for rule in self.investor_xpath_rules],
            'news_xpath_rules': [rule.xpath for rule in self.news_xpath_rules],
            'general_xpath_rules': [rule.xpath for rule in self.general_xpath_rules]
        }
    
    def get_url_xpath_mapping(self) -> Dict[str, Dict[str, List[str]]]:
        """
        获取URL到xpath规则的映射
        
        Returns:
            URL到xpath规则的映射字典
        """
        mapping = {}
        
        # 收集所有唯一的URL
        all_urls = set()
        for rule_type in ['investor', 'news', 'general']:
            rules = getattr(self, f"{rule_type}_xpath_rules")
            all_urls.update(rule.source_url for rule in rules)
        
        # 为每个URL创建映射
        for url in all_urls:
            mapping[url] = self.get_rules_by_url(url)
        
        return mapping
    
    def deduplicate(self) -> None:
        """去重xpath规则，保留第一个出现的规则"""
        for rule_type in ['investor', 'news', 'general']:
            rules = getattr(self, f"{rule_type}_xpath_rules")
            seen_xpaths = set()
            unique_rules = []
            
            for rule in rules:
                if rule.xpath not in seen_xpaths:
                    seen_xpaths.add(rule.xpath)
                    unique_rules.append(rule)
            
            setattr(self, f"{rule_type}_xpath_rules", unique_rules)
    
    def remove_cross_category_duplicates(self) -> None:
        """移除不同类别之间的重复规则，优先级：investor > news > general"""
        investor_xpaths = {rule.xpath for rule in self.investor_xpath_rules}
        news_xpaths = {rule.xpath for rule in self.news_xpath_rules}
        
        # 从news规则中移除与investor重复的规则
        self.news_xpath_rules = [
            rule for rule in self.news_xpath_rules 
            if rule.xpath not in investor_xpaths
        ]
        
        # 从general规则中移除与investor和news重复的规则
        updated_news_xpaths = {rule.xpath for rule in self.news_xpath_rules}
        self.general_xpath_rules = [
            rule for rule in self.general_xpath_rules 
            if rule.xpath not in investor_xpaths and rule.xpath not in updated_news_xpaths
        ]
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            'total_rules': len(self.investor_xpath_rules) + len(self.news_xpath_rules) + len(self.general_xpath_rules),
            'investor_rules_count': len(self.investor_xpath_rules),
            'news_rules_count': len(self.news_xpath_rules),
            'general_rules_count': len(self.general_xpath_rules),
            'unique_urls': len(set(
                rule.source_url for rules in [
                    self.investor_xpath_rules, 
                    self.news_xpath_rules, 
                    self.general_xpath_rules
                ] for rule in rules
            )),
            'avg_confidence': {
                'investor': sum(rule.confidence for rule in self.investor_xpath_rules) / len(self.investor_xpath_rules) if self.investor_xpath_rules else 0,
                'news': sum(rule.confidence for rule in self.news_xpath_rules) / len(self.news_xpath_rules) if self.news_xpath_rules else 0,
                'general': sum(rule.confidence for rule in self.general_xpath_rules) / len(self.general_xpath_rules) if self.general_xpath_rules else 0
            }
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为完整的字典格式"""
        return {
            'xpath_rules_with_sources': self.get_all_rules_with_sources(),
            'url_xpath_mapping': self.get_url_xpath_mapping(),
            'legacy_format': self.get_legacy_format(),
            'statistics': self.get_statistics()
        }
