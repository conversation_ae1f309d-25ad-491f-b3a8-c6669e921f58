# XPath与URL绑定关系修复方案

## 问题分析

### 当前问题
1. **数据结构缺陷**: xpath规则与来源URL没有建立正确的关联关系
2. **合并逻辑错误**: 在结果合并过程中，xpath规则失去了与原始URL的绑定
3. **可追溯性缺失**: 无法确定某个xpath规则来自哪个具体的URL
4. **分批处理问题**: 分批处理过程中xpath与URL的关联关系被破坏

### 具体问题位置
- `src/core/search_research.py:545-547`: xpath规则直接添加到全局列表
- `src/services/ai_analyzer.py:920-922`: 分批处理中同样的问题
- 缺少xpath与URL的映射数据结构

## 解决方案

### 1. 新的数据结构设计

#### 1.1 XPath规则项结构
```python
class XPathRuleItem:
    """单个XPath规则项，包含规则和来源信息"""
    def __init__(self, xpath: str, source_url: str, page_type: str, confidence: float = 0.0):
        self.xpath = xpath
        self.source_url = source_url
        self.page_type = page_type  # investor_relations, news_section, mixed_section
        self.confidence = confidence
        self.created_at = time.time()
    
    def to_dict(self):
        return {
            'xpath': self.xpath,
            'source_url': self.source_url,
            'page_type': self.page_type,
            'confidence': self.confidence,
            'created_at': self.created_at
        }
```

#### 1.2 分类XPath结果结构
```python
class ClassifiedXPathResult:
    """分类的XPath结果，维护xpath与URL的绑定关系"""
    def __init__(self):
        self.investor_xpath_rules: List[XPathRuleItem] = []
        self.news_xpath_rules: List[XPathRuleItem] = []
        self.general_xpath_rules: List[XPathRuleItem] = []
    
    def add_rules(self, xpath_list: List[str], source_url: str, page_type: str, 
                  rule_type: str, confidence: float = 0.0):
        """添加xpath规则并维护绑定关系"""
        target_list = getattr(self, f"{rule_type}_xpath_rules")
        for xpath in xpath_list:
            rule_item = XPathRuleItem(xpath, source_url, page_type, confidence)
            target_list.append(rule_item)
    
    def get_rules_by_url(self, url: str) -> Dict[str, List[str]]:
        """根据URL获取对应的xpath规则"""
        result = {
            'investor_xpath_rules': [],
            'news_xpath_rules': [],
            'general_xpath_rules': []
        }
        
        for rule_type in ['investor', 'news', 'general']:
            rules = getattr(self, f"{rule_type}_xpath_rules")
            result[f"{rule_type}_xpath_rules"] = [
                rule.xpath for rule in rules if rule.source_url == url
            ]
        
        return result
    
    def get_all_rules_with_sources(self) -> Dict[str, List[Dict]]:
        """获取所有规则及其来源信息"""
        return {
            'investor_xpath_rules': [rule.to_dict() for rule in self.investor_xpath_rules],
            'news_xpath_rules': [rule.to_dict() for rule in self.news_xpath_rules],
            'general_xpath_rules': [rule.to_dict() for rule in self.general_xpath_rules]
        }
    
    def get_legacy_format(self) -> Dict[str, List[str]]:
        """获取向后兼容的格式（仅xpath字符串列表）"""
        return {
            'investor_xpath_rules': [rule.xpath for rule in self.investor_xpath_rules],
            'news_xpath_rules': [rule.xpath for rule in self.news_xpath_rules],
            'general_xpath_rules': [rule.xpath for rule in self.general_xpath_rules]
        }
```

### 2. 修复方案实施

#### 2.1 修改AI分析器返回结构
- 在AI分析结果中包含source_url和page_type信息
- 更新prompt模板以支持新的数据结构

#### 2.2 修复结果合并逻辑
- 使用新的ClassifiedXPathResult类来管理xpath规则
- 在合并过程中保持xpath与URL的绑定关系
- 支持按URL查询和按类型查询

#### 2.3 更新分批处理逻辑
- 在分批处理中传递URL和页面类型信息
- 确保每个分片的结果都包含正确的来源信息

### 3. 实现步骤

1. **创建新的数据结构类**
   - 实现XPathRuleItem和ClassifiedXPathResult类
   - 添加必要的方法和属性

2. **修改AI分析器**
   - 更新analyze_classified_news_xpath方法
   - 更新analyze_classified_news_xpath_batch方法
   - 确保返回结果包含来源信息

3. **修复核心逻辑**
   - 修改_extract_classified_xpath_rules方法
   - 使用新的数据结构管理xpath规则
   - 保持向后兼容性

4. **更新结果输出**
   - 修改最终结果的数据结构
   - 添加xpath与URL的映射信息
   - 保持现有API的兼容性

### 4. 预期效果

修复后的系统将具备以下特性：

1. **完整的可追溯性**: 每个xpath规则都能追溯到其来源URL
2. **正确的绑定关系**: xpath规则与URL的关联关系得到正确维护
3. **分类管理**: 支持按页面类型和来源URL进行xpath规则分类
4. **向后兼容**: 保持现有API和数据格式的兼容性
5. **调试友好**: 便于调试和问题排查

### 5. 数据结构示例

修复后的结果结构示例：
```json
{
  "company_name": "TriSalus",
  "xpath_rules_with_sources": {
    "investor_xpath_rules": [
      {
        "xpath": "//div[@class='nir-widget--list']//a[@href]",
        "source_url": "https://investors.trisaluslifesci.com/investor-relations",
        "page_type": "investor_relations",
        "confidence": 0.90,
        "created_at": 1672531200.0
      }
    ],
    "news_xpath_rules": [
      {
        "xpath": "//article//h2//a[@href]",
        "source_url": "https://investors.trisaluslifesci.com/news-events/press-releases",
        "page_type": "investor_relations",
        "confidence": 0.85,
        "created_at": 1672531200.0
      }
    ]
  },
  "url_xpath_mapping": {
    "https://investors.trisaluslifesci.com/investor-relations": {
      "investor_xpath_rules": ["//div[@class='nir-widget--list']//a[@href]"],
      "news_xpath_rules": [],
      "general_xpath_rules": []
    },
    "https://investors.trisaluslifesci.com/news-events/press-releases": {
      "investor_xpath_rules": [],
      "news_xpath_rules": ["//article//h2//a[@href]"],
      "general_xpath_rules": []
    }
  },
  // 向后兼容的格式
  "investor_xpath_rules": ["//div[@class='nir-widget--list']//a[@href]"],
  "news_xpath_rules": ["//article//h2//a[@href]"],
  "general_xpath_rules": []
}
```
