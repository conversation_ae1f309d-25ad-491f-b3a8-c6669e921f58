{% extends "base.html" %}

{% block title %}系统日志 - AI工具新闻查找平台{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-file-alt me-2"></i>系统日志
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshLogs()">
                <i class="fas fa-sync-alt me-1"></i>刷新
            </button>
            <button type="button" class="btn btn-sm btn-outline-danger" onclick="clearLogs()">
                <i class="fas fa-trash me-1"></i>清空日志
            </button>
        </div>
    </div>
</div>

<!-- 日志过滤器 -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">日志过滤</h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <label for="logLevel" class="form-label">日志级别</label>
                <select class="form-select" id="logLevel" onchange="filterLogs()">
                    <option value="">全部</option>
                    <option value="INFO">信息</option>
                    <option value="WARNING">警告</option>
                    <option value="ERROR">错误</option>
                    <option value="DEBUG">调试</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="dateFrom" class="form-label">开始日期</label>
                <input type="date" class="form-control" id="dateFrom" onchange="filterLogs()">
            </div>
            <div class="col-md-3">
                <label for="dateTo" class="form-label">结束日期</label>
                <input type="date" class="form-control" id="dateTo" onchange="filterLogs()">
            </div>
            <div class="col-md-3">
                <label for="searchKeyword" class="form-label">关键词搜索</label>
                <input type="text" class="form-control" id="searchKeyword" placeholder="搜索日志内容..." onkeyup="filterLogs()">
            </div>
        </div>
    </div>
</div>

<!-- 实时日志显示 -->
<div class="card shadow mb-4">
    <div class="card-header py-3 d-flex justify-content-between align-items-center">
        <h6 class="m-0 font-weight-bold text-primary">实时日志</h6>
        <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="autoRefresh" checked>
            <label class="form-check-label" for="autoRefresh">自动刷新</label>
        </div>
    </div>
    <div class="card-body">
        <div id="logContainer" class="log-container" style="height: 500px; overflow-y: auto; background-color: #1e1e1e; color: #ffffff; font-family: 'Courier New', monospace; padding: 15px; border-radius: 5px;">
            <div class="log-entry log-info">
                <span class="log-timestamp">[2025-07-03 08:00:00]</span>
                <span class="log-level">[INFO]</span>
                <span class="log-message">系统启动完成</span>
            </div>
            <div class="log-entry log-info">
                <span class="log-timestamp">[2025-07-03 08:00:01]</span>
                <span class="log-level">[INFO]</span>
                <span class="log-message">数据库连接成功</span>
            </div>
            <div class="log-entry log-info">
                <span class="log-timestamp">[2025-07-03 08:00:02]</span>
                <span class="log-level">[INFO]</span>
                <span class="log-message">Web服务器启动，监听端口 5000</span>
            </div>
            <div class="log-entry log-warning">
                <span class="log-timestamp">[2025-07-03 08:00:03]</span>
                <span class="log-level">[WARNING]</span>
                <span class="log-message">这是一个示例警告日志</span>
            </div>
            <div class="log-entry log-success">
                <span class="log-timestamp">[2025-07-03 08:00:04]</span>
                <span class="log-level">[SUCCESS]</span>
                <span class="log-message">用户 admin 登录成功</span>
            </div>
        </div>
    </div>
</div>

<!-- 日志统计 -->
<div class="row">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            今日日志总数
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalLogs">
                            1,234
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-file-alt fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            警告日志
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="warningLogs">
                            23
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-danger shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                            错误日志
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="errorLogs">
                            5
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-times-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            系统运行时间
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="uptime">
                            2天 3小时
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clock fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.log-container {
    background-color: #1e1e1e !important;
    color: #ffffff !important;
}

.log-entry {
    margin-bottom: 5px;
    padding: 2px 0;
    font-size: 0.9rem;
}

.log-timestamp {
    color: #888;
}

.log-level {
    font-weight: bold;
    margin: 0 10px;
}

.log-info .log-level {
    color: #17a2b8;
}

.log-warning .log-level {
    color: #ffc107;
}

.log-error .log-level {
    color: #dc3545;
}

.log-success .log-level {
    color: #28a745;
}

.log-message {
    color: #ffffff;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.border-left-danger {
    border-left: 0.25rem solid #e74a3b !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.text-gray-300 {
    color: #dddfeb !important;
}

.text-gray-800 {
    color: #5a5c69 !important;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
let autoRefreshInterval;

function filterLogs() {
    // 这里可以添加实际的日志过滤逻辑
    console.log('过滤日志...');
}

function refreshLogs() {
    // 这里可以添加实际的日志刷新逻辑
    showAlert('日志已刷新', 'success');
}

function clearLogs() {
    if (!confirm('确定要清空所有日志吗？此操作不可恢复。')) {
        return;
    }
    
    // 这里可以添加实际的清空日志逻辑
    $('#logContainer').html('<div class="text-center text-muted">日志已清空</div>');
    showAlert('日志已清空', 'success');
}

function addLogEntry(timestamp, level, message) {
    const logEntry = `
        <div class="log-entry log-${level.toLowerCase()}">
            <span class="log-timestamp">[${timestamp}]</span>
            <span class="log-level">[${level}]</span>
            <span class="log-message">${message}</span>
        </div>
    `;
    
    $('#logContainer').append(logEntry);
    
    // 自动滚动到底部
    const container = document.getElementById('logContainer');
    container.scrollTop = container.scrollHeight;
}

function startAutoRefresh() {
    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
    }
    
    autoRefreshInterval = setInterval(() => {
        if ($('#autoRefresh').is(':checked')) {
            // 模拟新日志条目
            const now = new Date().toISOString().slice(0, 19).replace('T', ' ');
            const levels = ['INFO', 'WARNING', 'ERROR', 'SUCCESS'];
            const messages = [
                '用户执行搜索操作',
                '数据库查询完成',
                'AI分析任务启动',
                '缓存更新成功',
                '系统健康检查通过'
            ];
            
            const level = levels[Math.floor(Math.random() * levels.length)];
            const message = messages[Math.floor(Math.random() * messages.length)];
            
            addLogEntry(now, level, message);
        }
    }, 5000); // 每5秒添加一条新日志
}

$(document).ready(function() {
    console.log('系统日志页面已加载');
    
    // 启动自动刷新
    startAutoRefresh();
    
    // 监听自动刷新开关
    $('#autoRefresh').change(function() {
        if ($(this).is(':checked')) {
            startAutoRefresh();
        } else {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
            }
        }
    });
});
</script>
{% endblock %}
