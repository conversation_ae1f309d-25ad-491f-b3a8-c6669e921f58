{% extends "base.html" %}

{% block title %}数据源管理 - AI工具新闻查找平台{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-database me-2"></i>数据源管理
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-primary" onclick="showAddModal()">
                <i class="fas fa-plus me-1"></i>添加数据源
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshTable()">
                <i class="fas fa-sync-alt me-1"></i>刷新
            </button>
        </div>
    </div>
</div>

<!-- 数据源列表 -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">数据源列表</h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered" id="dataSourcesTable" width="100%" cellspacing="0">
                <thead>
                    <tr>
                        <th>名称</th>
                        <th>类型</th>
                        <th>状态</th>
                        <th>描述</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for source in sources %}
                    <tr>
                        <td>{{ source.name }}</td>
                        <td>
                            {% if source.source_type == 'google_search' %}
                                <span class="badge bg-info">Google搜索</span>
                            {% elif source.source_type == 'mysql' %}
                                <span class="badge bg-warning">MySQL</span>
                            {% elif source.source_type == 'elasticsearch' %}
                                <span class="badge bg-dark">Elasticsearch</span>
                            {% else %}
                                <span class="badge bg-secondary">{{ source.source_type }}</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if source.enabled %}
                                <span class="badge bg-success">启用</span>
                            {% else %}
                                <span class="badge bg-secondary">禁用</span>
                            {% endif %}
                        </td>
                        <td>
                            <span class="text-truncate d-inline-block" style="max-width: 200px;" title="{{ source.description or '无描述' }}">
                                {{ source.description or '无描述' }}
                            </span>
                        </td>
                        <td>{{ source.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <button type="button" class="btn btn-outline-info" onclick="testConnection({{ source.id }})">
                                    <i class="fas fa-plug"></i>
                                </button>
                                <button type="button" class="btn btn-outline-primary" onclick="editSource({{ source.id }})">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" class="btn btn-outline-danger" onclick="deleteSource({{ source.id }})">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 添加/编辑数据源模态框 -->
<div class="modal fade" id="dataSourceModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="dataSourceModalTitle">添加数据源</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="dataSourceForm">
                    <input type="hidden" id="sourceId" name="sourceId">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sourceName" class="form-label">名称 *</label>
                                <input type="text" class="form-control" id="sourceName" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sourceType" class="form-label">类型 *</label>
                                <select class="form-select" id="sourceType" name="source_type" required onchange="updateConfigFields()">
                                    <option value="">请选择数据源类型</option>
                                    <option value="google_search">Google搜索</option>
                                    <option value="mysql">MySQL数据库</option>
                                    <option value="elasticsearch">Elasticsearch</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">描述</label>
                        <textarea class="form-control" id="description" name="description" rows="2"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="enabled" name="enabled" checked>
                            <label class="form-check-label" for="enabled">启用此数据源</label>
                        </div>
                    </div>
                    
                    <!-- 配置参数区域 -->
                    <div id="configSection">
                        <label class="form-label">配置参数</label>
                        
                        <!-- Google搜索配置 -->
                        <div id="googleConfig" class="config-group" style="display: none;">
                            <div class="row">
                                <div class="col-md-8">
                                    <label for="apiUrl" class="form-label">API URL</label>
                                    <input type="url" class="form-control" id="apiUrl" name="api_url">
                                </div>
                                <div class="col-md-4">
                                    <label for="maxResults" class="form-label">最大结果数</label>
                                    <input type="number" class="form-control" id="maxResults" name="max_results" value="10">
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-md-6">
                                    <label for="timeout" class="form-label">超时时间(秒)</label>
                                    <input type="number" class="form-control" id="timeout" name="timeout" value="30">
                                </div>
                            </div>
                        </div>
                        
                        <!-- MySQL配置 -->
                        <div id="mysqlConfig" class="config-group" style="display: none;">
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="host" class="form-label">主机地址</label>
                                    <input type="text" class="form-control" id="host" name="host" value="localhost">
                                </div>
                                <div class="col-md-6">
                                    <label for="port" class="form-label">端口</label>
                                    <input type="number" class="form-control" id="port" name="port" value="3306">
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-md-6">
                                    <label for="database" class="form-label">数据库名</label>
                                    <input type="text" class="form-control" id="database" name="database">
                                </div>
                                <div class="col-md-6">
                                    <label for="username" class="form-label">用户名</label>
                                    <input type="text" class="form-control" id="username" name="username">
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-md-6">
                                    <label for="password" class="form-label">密码</label>
                                    <input type="password" class="form-control" id="password" name="password">
                                </div>
                            </div>
                        </div>
                        
                        <!-- Elasticsearch配置 -->
                        <div id="elasticsearchConfig" class="config-group" style="display: none;">
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="esHost" class="form-label">主机地址</label>
                                    <input type="text" class="form-control" id="esHost" name="es_host" value="localhost">
                                </div>
                                <div class="col-md-6">
                                    <label for="esPort" class="form-label">端口</label>
                                    <input type="number" class="form-control" id="esPort" name="es_port" value="9200">
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-md-6">
                                    <label for="index" class="form-label">索引名</label>
                                    <input type="text" class="form-control" id="index" name="index">
                                </div>
                                <div class="col-md-6">
                                    <label for="esUsername" class="form-label">用户名</label>
                                    <input type="text" class="form-control" id="esUsername" name="es_username">
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-md-6">
                                    <label for="esPassword" class="form-label">密码</label>
                                    <input type="password" class="form-control" id="esPassword" name="es_password">
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveSource()">保存</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentSourceId = null;

function updateConfigFields() {
    const sourceType = $('#sourceType').val();
    $('.config-group').hide();

    if (sourceType === 'google_search') {
        $('#googleConfig').show();
    } else if (sourceType === 'mysql') {
        $('#mysqlConfig').show();
    } else if (sourceType === 'elasticsearch') {
        $('#elasticsearchConfig').show();
    }
}

function showAddModal() {
    currentSourceId = null;
    $('#dataSourceModalTitle').text('添加数据源');
    $('#dataSourceForm')[0].reset();
    $('#sourceId').val('');
    $('#enabled').prop('checked', true);
    $('.config-group').hide();
    $('#dataSourceModal').modal('show');
}

function editSource(sourceId) {
    currentSourceId = sourceId;
    $('#dataSourceModalTitle').text('编辑数据源');

    // 获取数据源详情
    $.ajax({
        url: `/admin/api/data-sources/${sourceId}`,
        method: 'GET',
        success: function(response) {
            if (response.success) {
                const source = response.data;
                $('#sourceId').val(source.id);
                $('#sourceName').val(source.name);
                $('#sourceType').val(source.source_type);
                $('#description').val(source.description || '');
                $('#enabled').prop('checked', source.enabled);

                // 更新配置字段显示
                updateConfigFields();

                // 填充配置参数
                const config = source.config_params || {};

                if (source.source_type === 'google_search') {
                    $('#apiUrl').val(config.api_url || '');
                    $('#maxResults').val(config.max_results || 10);
                    $('#timeout').val(config.timeout || 30);
                } else if (source.source_type === 'mysql') {
                    $('#host').val(config.host || 'localhost');
                    $('#port').val(config.port || 3306);
                    $('#database').val(config.database || '');
                    $('#username').val(config.username || '');
                } else if (source.source_type === 'elasticsearch') {
                    $('#esHost').val(config.host || 'localhost');
                    $('#esPort').val(config.port || 9200);
                    $('#index').val(config.index || '');
                    $('#esUsername').val(config.username || '');
                }

                $('#dataSourceModal').modal('show');
            } else {
                showAlert(response.message, 'danger');
            }
        },
        error: function() {
            showAlert('获取数据源信息失败', 'danger');
        }
    });
}

function saveSource() {
    const sourceType = $('#sourceType').val();

    if (!sourceType) {
        showAlert('请选择数据源类型', 'danger');
        return;
    }

    const formData = {
        name: $('#sourceName').val().trim(),
        source_type: sourceType,
        description: $('#description').val().trim(),
        enabled: $('#enabled').is(':checked'),
        config_params: {}
    };

    // 验证必要字段
    if (!formData.name) {
        showAlert('请填写数据源名称', 'danger');
        return;
    }

    // 根据类型收集配置参数
    if (sourceType === 'google_search') {
        formData.config_params = {
            api_url: $('#apiUrl').val().trim(),
            max_results: parseInt($('#maxResults').val()) || 10,
            timeout: parseInt($('#timeout').val()) || 30
        };
    } else if (sourceType === 'mysql') {
        formData.config_params = {
            host: $('#host').val().trim() || 'localhost',
            port: parseInt($('#port').val()) || 3306,
            database: $('#database').val().trim(),
            username: $('#username').val().trim(),
            password: $('#password').val()
        };
    } else if (sourceType === 'elasticsearch') {
        formData.config_params = {
            host: $('#esHost').val().trim() || 'localhost',
            port: parseInt($('#esPort').val()) || 9200,
            index: $('#index').val().trim(),
            username: $('#esUsername').val().trim(),
            password: $('#esPassword').val()
        };
    }

    const url = currentSourceId ?
        `/admin/api/data-sources/${currentSourceId}` :
        '/admin/api/data-sources';
    const method = currentSourceId ? 'PUT' : 'POST';

    $.ajax({
        url: url,
        method: method,
        contentType: 'application/json',
        data: JSON.stringify(formData),
        success: function(response) {
            if (response.success) {
                showAlert(response.message, 'success');
                $('#dataSourceModal').modal('hide');
                refreshTable();
            } else {
                showAlert(response.message, 'danger');
            }
        },
        error: function(xhr) {
            let message = '操作失败';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                message = xhr.responseJSON.message;
            }
            showAlert(message, 'danger');
        }
    });
}

function deleteSource(sourceId) {
    if (!confirm('确定要删除这个数据源吗？此操作不可恢复。')) {
        return;
    }

    $.ajax({
        url: `/admin/api/data-sources/${sourceId}`,
        method: 'DELETE',
        success: function(response) {
            if (response.success) {
                showAlert(response.message, 'success');
                refreshTable();
            } else {
                showAlert(response.message, 'danger');
            }
        },
        error: function() {
            showAlert('删除失败', 'danger');
        }
    });
}

function testConnection(sourceId) {
    const btn = event.target.closest('button');
    const originalHtml = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    btn.disabled = true;

    $.ajax({
        url: `/admin/api/data-sources/${sourceId}/test`,
        method: 'POST',
        success: function(response) {
            if (response.success) {
                showAlert(response.message, 'success');
            } else {
                showAlert(response.message, 'warning');
            }
        },
        error: function() {
            showAlert('连接测试失败', 'danger');
        },
        complete: function() {
            btn.innerHTML = originalHtml;
            btn.disabled = false;
        }
    });
}

function refreshTable() {
    location.reload();
}

$(document).ready(function() {
    // 初始化页面
    console.log('数据源管理页面已加载');
});
</script>
{% endblock %}
