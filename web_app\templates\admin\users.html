{% extends "base.html" %}

{% block title %}用户管理 - AI工具新闻查找平台{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-users me-2"></i>用户管理
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshTable()">
                <i class="fas fa-sync-alt me-1"></i>刷新
            </button>
        </div>
    </div>
</div>

<!-- 用户列表 -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">用户列表</h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered" id="usersTable" width="100%" cellspacing="0">
                <thead>
                    <tr>
                        <th>用户名</th>
                        <th>邮箱</th>
                        <th>角色</th>
                        <th>状态</th>
                        <th>最后登录</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in users %}
                    <tr>
                        <td>{{ user.username }}</td>
                        <td>{{ user.email }}</td>
                        <td>
                            {% if user.role == 'admin' %}
                                <span class="badge bg-warning text-dark">管理员</span>
                            {% else %}
                                <span class="badge bg-info">普通用户</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if user.is_active %}
                                <span class="badge bg-success">活跃</span>
                            {% else %}
                                <span class="badge bg-secondary">禁用</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if user.last_login %}
                                {{ user.last_login.strftime('%Y-%m-%d %H:%M') }}
                            {% else %}
                                <span class="text-muted">从未登录</span>
                            {% endif %}
                        </td>
                        <td>{{ user.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                {% if user.username != current_user.username %}
                                <button type="button" class="btn btn-outline-warning" 
                                        onclick="toggleUserStatus({{ user.id }}, {{ user.is_active|lower }})">
                                    {% if user.is_active %}
                                        <i class="fas fa-user-slash"></i>
                                    {% else %}
                                        <i class="fas fa-user-check"></i>
                                    {% endif %}
                                </button>
                                {% endif %}
                                <button type="button" class="btn btn-outline-info" onclick="viewUserDetails({{ user.id }})">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 用户详情模态框 -->
<div class="modal fade" id="userDetailsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">用户详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="userDetailsContent">
                <!-- 用户详情内容将通过JavaScript填充 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function toggleUserStatus(userId, currentStatus) {
    const action = currentStatus ? '禁用' : '启用';
    if (!confirm(`确定要${action}这个用户吗？`)) {
        return;
    }
    
    // 这里可以添加实际的用户状态切换API调用
    showAlert(`用户${action}功能待实现`, 'info');
}

function viewUserDetails(userId) {
    // 这里可以添加获取用户详情的API调用
    $('#userDetailsContent').html(`
        <div class="text-center">
            <i class="fas fa-spinner fa-spin fa-2x mb-3"></i>
            <p>加载用户详情...</p>
        </div>
    `);
    $('#userDetailsModal').modal('show');
    
    // 模拟加载用户详情
    setTimeout(() => {
        $('#userDetailsContent').html(`
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                用户详情功能待实现
            </div>
        `);
    }, 1000);
}

function refreshTable() {
    location.reload();
}

$(document).ready(function() {
    console.log('用户管理页面已加载');
});
</script>
{% endblock %}
