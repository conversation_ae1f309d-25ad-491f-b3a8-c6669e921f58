"""
普通用户路由
"""
from flask import Blueprint, render_template, request, jsonify
from flask_login import current_user
from web_app.models.ai_source import AISource
from web_app.models.data_source import DataSource
from web_app.utils.decorators import login_required

user_bp = Blueprint('user', __name__, url_prefix='/user')


@user_bp.route('/dashboard')
@login_required
def dashboard():
    """用户仪表板"""
    # 获取可用的AI源和数据源
    ai_sources = AISource.get_enabled_sources()
    data_sources = DataSource.get_enabled_sources()
    
    return render_template('user/dashboard.html', 
                         ai_sources=ai_sources,
                         data_sources=data_sources)


@user_bp.route('/search')
@login_required
def search():
    """搜索任务页面"""
    # 获取可用的AI源和数据源
    ai_sources = AISource.get_enabled_sources()
    data_sources = DataSource.get_enabled_sources()
    
    return render_template('user/search.html', 
                         ai_sources=ai_sources,
                         data_sources=data_sources)


@user_bp.route('/history')
@login_required
def history():
    """搜索历史页面"""
    return render_template('user/history.html')


@user_bp.route('/api/available-sources')
@login_required
def api_available_sources():
    """获取可用的AI源和数据源API"""
    ai_sources = AISource.get_enabled_sources()
    data_sources = DataSource.get_enabled_sources()
    
    return jsonify({
        'success': True,
        'data': {
            'ai_sources': [source.to_dict() for source in ai_sources],
            'data_sources': [source.to_dict() for source in data_sources]
        }
    })


@user_bp.route('/api/search', methods=['POST'])
@login_required
def api_search():
    """执行搜索任务API"""
    if not request.is_json:
        return jsonify({'success': False, 'message': '需要JSON格式'}), 400
    
    data = request.get_json()
    
    # 验证必要字段
    required_fields = ['ai_source_id', 'data_source_id', 'company_name']
    for field in required_fields:
        if not data.get(field):
            return jsonify({
                'success': False,
                'message': f'缺少必要字段: {field}'
            }), 400
    
    try:
        # 验证AI源和数据源是否存在且启用
        ai_source = AISource.query.filter_by(
            id=data['ai_source_id'], 
            enabled=True
        ).first()
        if not ai_source:
            return jsonify({
                'success': False,
                'message': 'AI源不存在或已禁用'
            }), 400
        
        data_source = DataSource.query.filter_by(
            id=data['data_source_id'], 
            enabled=True
        ).first()
        if not data_source:
            return jsonify({
                'success': False,
                'message': '数据源不存在或已禁用'
            }), 400
        
        # 这里将集成实际的搜索功能
        # 暂时返回模拟结果
        return jsonify({
            'success': True,
            'message': '搜索任务已启动',
            'data': {
                'task_id': 'mock_task_123',
                'status': 'started',
                'ai_source': ai_source.to_dict(),
                'data_source': data_source.to_dict(),
                'company_name': data['company_name']
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'搜索启动失败: {str(e)}'
        }), 500
