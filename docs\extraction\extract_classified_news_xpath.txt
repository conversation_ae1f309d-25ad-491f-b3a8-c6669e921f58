你是一个专业的网页结构分析和XPath专家。请分析以下新闻板块页面的HTML内容，提取新闻链接列表的XPath选择器规则，并根据页面类型进行分类。

公司名称：{company_name}
页面URL：{page_url}
页面类型：{page_type}  # investor_relations, news_section, mixed_section
HTML内容：
{html_content}

请按照以下要求分析：

1. 页面类型识别：
   
   **投资者关系页面**：
   - 重点关注财务报告、SEC文件、股东信息、治理文档等链接
   - 寻找季报、年报、投资者公告、股价信息等内容
   - 关注日期格式通常为财务周期（Q1, Q2等）
   
   **一般新闻页面**：
   - 重点关注公司新闻、产品发布、媒体报道、行业动态等链接
   - 寻找新闻标题、发布日期、新闻摘要等内容
   - 关注日期格式通常为标准日期格式
   
   **混合页面**：
   - 同时包含投资者信息和一般新闻的页面
   - 需要区分不同类型的内容区域
   - 可能有多个不同的列表或分区

2. 分析步骤：
   - 识别页面的主要内容区域和导航结构
   - 查找包含新闻/信息列表的容器元素
   - 分析列表项的HTML结构和内容类型
   - 识别链接元素的选择器模式
   - 根据内容类型分类XPath规则

3. XPath规则分类：
   
   **投资者关系XPath**：
   - 专门用于提取投资者相关内容的XPath
   - 如财务报告、SEC文件、股东会议等
   
   **新闻内容XPath**：
   - 专门用于提取一般新闻内容的XPath
   - 如公司新闻、产品发布、媒体报道等
   
   **通用XPath**：
   - 可以同时提取多种类型内容的XPath
   - 适用于混合页面或结构相似的内容

4. XPath规则要求：
   - 提供多个可能的XPath规则（按优先级排序）
   - 确保XPath能准确选择到目标链接
   - 考虑页面结构的变化可能性
   - 区分不同类型内容的选择器

5. 常见模式识别：
   - 新闻列表：ul/li、div、table、article结构
   - 链接元素：a标签，包含href属性
   - 内容分类：通过class、id、data属性区分
   - 日期信息：时间戳、格式化日期、相对时间

6. 输出要求：
   请以JSON格式返回结果：
   ```json
   {
     "investor_xpath_rules": [
       "//div[@class='investor-news']//a[@href]",
       "//section[@id='financial-reports']//li//a",
       "//table[@class='sec-filings']//td//a[@href]"
     ],
     "news_xpath_rules": [
       "//div[@class='company-news']//a[@href]",
       "//ul[@id='press-releases']//li//a",
       "//article[@class='news-item']//h2//a"
     ],
     "general_xpath_rules": [
       "//div[@class='news-list']//a[@href]",
       "//section[@class='content-list']//a[@href]"
     ],
     "primary_xpath": {
       "investor": "//div[@class='investor-news']//a[@href]",
       "news": "//div[@class='company-news']//a[@href]",
       "general": "//div[@class='news-list']//a[@href]"
     },
     "confidence": 0.85,
     "page_structure": "页面结构的详细分析",
     "content_classification": "内容分类的依据和说明",
     "sample_links": {
       "investor": [
         "https://example.com/investor/q3-2024-earnings",
         "https://example.com/sec-filing/10k-2024"
       ],
       "news": [
         "https://example.com/news/product-launch-2024",
         "https://example.com/press/partnership-announcement"
       ]
     }
   }
   ```

7. 特殊考虑：
   - **动态内容**：考虑JavaScript动态加载的内容
   - **分页结构**：识别分页导航和"更多"链接
   - **过滤器**：识别日期过滤、分类过滤等功能
   - **搜索功能**：识别站内搜索和过滤功能
   - **RSS/订阅**：识别RSS链接和订阅功能

注意：
- 如果某个分类没有找到相关XPath，返回空数组
- primary_xpath提供每个分类的最推荐XPath规则
- confidence表示XPath规则的整体可靠性
- sample_links提供找到的示例链接（每类最多5个）
- page_structure要详细描述页面的HTML结构
- content_classification要说明内容分类的依据
