{% extends "base.html" %}

{% block title %}访问被拒绝 - AI工具新闻查找平台{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-6">
            <div class="text-center mt-5">
                <div class="error mx-auto" data-text="403">403</div>
                <p class="lead text-gray-800 mb-5">访问被拒绝</p>
                <p class="text-gray-500 mb-0">您没有权限访问此页面...</p>
                <a href="{{ url_for('index') }}">&larr; 返回首页</a>
            </div>
        </div>
    </div>
</div>

<style>
.error {
    font-size: 7rem;
    position: relative;
    line-height: 1;
    width: 12.5rem;
}

.error:before {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    background: linear-gradient(45deg, transparent 25%, rgba(255, 255, 255, 0.5) 50%, transparent 75%);
    color: #f8f9fa;
    mix-blend-mode: overlay;
    animation: shine 3s infinite linear;
}

@keyframes shine {
    0% { left: -100%; }
    60% { left: 100%; }
    100% { left: 100%; }
}
</style>
{% endblock %}
