2025-07-02 18:10:32,447 - __main__ - INFO - AI Tools Find News - 公司调研工具启动
2025-07-02 18:10:32,447 - __main__ - INFO - 准备调研 1 个公司
2025-07-02 18:10:32,447 - __main__ - INFO - 开始调研第 1/1 个公司: TriSalus
2025-07-02 18:10:32,448 - src.services.google_search - INFO - Google搜索服务初始化完成
2025-07-02 18:10:32,448 - src.services.web_scraper - INFO - 网页抓取服务初始化完成
2025-07-02 18:10:32,515 - src.services.ai_analyzer - INFO - AI分析服务初始化完成
2025-07-02 18:10:32,516 - src.core.search_research - INFO - SearchResearchClass 初始化完成
2025-07-02 18:10:32,516 - src.core.search_research - INFO - 开始调研公司: TriSalus
2025-07-02 18:10:32,519 - src.core.search_research - INFO - 步骤1: 搜索公司官网
2025-07-02 18:10:32,529 - src.services.google_search - INFO - 执行Google搜索: TriSalus
2025-07-02 18:10:35,054 - src.services.google_search - INFO - 搜索成功，返回 10 个结果
2025-07-02 18:10:35,056 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 18:10:37,945 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 288
2025-07-02 18:10:37,945 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 18:10:37,945 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "official_website": "https://trisaluslifesci.com/",
  "confidence": 0.98,
  "reasoning": "选择了https://trisaluslifesci.com/作为TriSalus的官方网站，因为该URL包含公司名称，并且是一个主流的.com域名。该页面的标题为'Trisalus Life Sciences'，与公司名称完全一致，且该URL没有指向任何子页面，而是直接指向公司的主页。此外，其他搜索结果均为子页面或投资者关系页面，缺乏作为官方网站的特征。"
}
```
2025-07-02 18:10:37,945 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 18:10:37,945 - src.core.search_research - INFO - 找到官网: https://trisaluslifesci.com/
2025-07-02 18:10:37,945 - src.core.search_research - INFO - 步骤2: 分析官网首页
2025-07-02 18:10:37,945 - src.services.web_scraper - INFO - 开始抓取页面: https://trisaluslifesci.com/
2025-07-02 18:10:37,946 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://trisaluslifesci.com/
2025-07-02 18:10:38,076 - src.services.web_scraper - WARNING - requests页面抓取失败，状态码: 403, URL: https://trisaluslifesci.com/
2025-07-02 18:10:39,079 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://trisaluslifesci.com/
2025-07-02 18:10:39,203 - src.services.web_scraper - WARNING - requests页面抓取失败，状态码: 403, URL: https://trisaluslifesci.com/
2025-07-02 18:10:39,203 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://trisaluslifesci.com/
2025-07-02 18:10:39,622 - src.services.drission_scraper - INFO - DrissionPage抓取服务初始化完成
2025-07-02 18:10:39,622 - src.services.web_scraper - INFO - DrissionPage抓取服务已初始化
2025-07-02 18:10:39,622 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://trisaluslifesci.com/
2025-07-02 18:10:40,579 - src.services.drission_scraper - INFO - ChromiumPage实例创建成功
2025-07-02 18:10:48,213 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://trisaluslifesci.com/, 内容长度: 275975
2025-07-02 18:10:48,387 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://trisaluslifesci.com/, 内容长度: 197692
2025-07-02 18:10:48,388 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 18:10:52,390 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 994
2025-07-02 18:10:52,390 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 18:10:52,390 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_relations_urls": [
    "https://investors.trisaluslifesci.com/investor-relations",
    "https://investors.trisaluslifesci.com/news-events/press-releases",
    "https://investors.trisaluslifesci.com/news-events/events-presentations",
    "https://investors.trisaluslifesci.com/governance/governance-overview",
    "https://investors.trisaluslifesci.com/financials/sec-filings",
    "https://investors.trisaluslifesci.com/financials/quarterly-results",
    "https://investors.trisaluslifesci.com/financials/annual-reports",
    "https://investors.trisaluslifesci.com/stock-information",
    "https://investors.trisaluslifesci.com/investor-resources/email-alerts",
    "https://investors.trisaluslifesci.com/investor-resources/investor-contacts"
  ],
  "found_keywords": ["投资者关系", "投资者", "Financials", "SEC Filings", "Annual Reports"],
  "confidence": 0.95,
  "analysis_notes": "在分析过程中，识别了多个与投资者关系相关的链接，主要集中在导航菜单中。所有链接均为绝对链接，且没有重复。关键词包括'投资者关系'、'投资者'、'财务信息'等，显示出较高的置信度。"
}
```
2025-07-02 18:10:52,390 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 18:10:52,390 - src.core.search_research - INFO - 找到投资者关系页面: 10个
2025-07-02 18:10:52,390 - src.core.search_research - INFO - 步骤3: 分析投资者关系页面
2025-07-02 18:10:52,390 - src.core.search_research - INFO - 分析投资者关系页面: https://investors.trisaluslifesci.com/investor-relations
2025-07-02 18:10:52,390 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/investor-relations
2025-07-02 18:10:52,390 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/investor-relations
2025-07-02 18:10:57,542 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /investor-relations (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/investor-relations
2025-07-02 18:10:58,222 - src.services.drission_scraper - WARNING - 关闭DrissionPage浏览器实例时出现警告: 
超时，可能是浏览器卡了。
详情: timeout
方法: Browser.close
参数: {'_timeout': 30}
版本: ********
2025-07-02 18:10:58,222 - src.services.web_scraper - INFO - DrissionPage资源已清理
2025-07-02 18:10:58,542 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/investor-relations
2025-07-02 18:11:03,637 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /investor-relations (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/investor-relations
2025-07-02 18:11:03,637 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/investor-relations
2025-07-02 18:11:03,637 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/investor-relations
2025-07-02 18:11:14,691 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/investor-relations, 内容长度: 188291
2025-07-02 18:11:14,753 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/investor-relations, 内容长度: 45954
2025-07-02 18:11:14,754 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 18:11:19,457 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 800
2025-07-02 18:11:19,457 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 18:11:19,457 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "xpath_rules": [
    "//div[contains(@class, 'block--nir-news__widget')]//a[@href]",
    "//div[contains(@class, 'nir-widget--news--headline')]//a",
    "//section[contains(@class, 'block--news-grid')]//a[@href]"
  ],
  "primary_xpath": "//div[contains(@class, 'block--nir-news__widget')]//a[@href]",
  "confidence": 0.90,
  "structure_analysis": "页面中新闻列表位于class为'block--nir-news__widget'的div中，包含多个新闻项，每个新闻项的标题和链接都在a标签内。每个新闻项的日期信息在p标签中，确保了新闻的时间戳存在。",
  "sample_links": [
    "/news-releases/news-release-details/trisalus-life-sciences-announces-commencement-exchange-offer-0",
    "/news-releases/news-release-details/trisalus-life-sciences-announces-launch-trinav-flx-infusion",
    "/news-releases/news-release-details/trisalus-life-sciences-announces-chief-financial-officer"
  ]
}
```
2025-07-02 18:11:19,457 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 18:11:19,457 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/investor-relations 提取到 3 个XPath规则
2025-07-02 18:11:20,464 - src.core.search_research - INFO - 分析投资者关系页面: https://investors.trisaluslifesci.com/news-events/press-releases
2025-07-02 18:11:20,464 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/news-events/press-releases
2025-07-02 18:11:20,464 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/news-events/press-releases
2025-07-02 18:11:25,560 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /news-events/press-releases (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/news-events/press-releases
2025-07-02 18:11:26,562 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/news-events/press-releases
2025-07-02 18:11:28,428 - src.services.drission_scraper - WARNING - 关闭DrissionPage浏览器实例时出现警告: 
超时，可能是浏览器卡了。
详情: timeout
方法: Browser.close
参数: {'_timeout': 30}
版本: ********
2025-07-02 18:11:31,641 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /news-events/press-releases (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/news-events/press-releases
2025-07-02 18:11:31,641 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/news-events/press-releases
2025-07-02 18:11:31,641 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/news-events/press-releases
2025-07-02 18:11:39,728 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/news-events/press-releases, 内容长度: 193750
2025-07-02 18:11:39,794 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/news-events/press-releases, 内容长度: 52073
2025-07-02 18:11:39,795 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 18:11:45,748 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 1172
2025-07-02 18:11:45,748 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 18:11:45,748 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "xpath_rules": [
    "//div[contains(@class, 'nir-widget--list')]//a[@href]",
    "//article[contains(@class, 'node--nir-news--nir-widget-list')]//a[@href]",
    "//div[@id='lfg-content']//a[contains(@href, '/news-releases/')]"
  ],
  "primary_xpath": "//div[contains(@class, 'nir-widget--list')]//a[@href]",
  "confidence": 0.90,
  "structure_analysis": "页面包含一个主要的新闻列表区域，使用了多个article元素来展示每条新闻。每个article元素中包含一个a标签，链接到具体的新闻发布页面。新闻的标题和链接都在这些a标签中，且每个新闻条目都包含日期信息，确保了信息的完整性。",
  "sample_links": [
    "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-commencement-exchange-offer-0",
    "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-launch-trinav-flx-infusion",
    "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-chief-financial-officer",
    "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-reports-first-quarter-2025-results-and",
    "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-to-host-first-quarter-2025-financial-results"
  ]
}
```
2025-07-02 18:11:45,748 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 18:11:45,748 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/news-events/press-releases 提取到 3 个XPath规则
2025-07-02 18:11:46,761 - src.core.search_research - INFO - 分析投资者关系页面: https://investors.trisaluslifesci.com/news-events/events-presentations
2025-07-02 18:11:46,761 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/news-events/events-presentations
2025-07-02 18:11:46,761 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/news-events/events-presentations
2025-07-02 18:11:51,854 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /news-events/events-presentations (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/news-events/events-presentations
2025-07-02 18:11:52,861 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/news-events/events-presentations
2025-07-02 18:11:57,947 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /news-events/events-presentations (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/news-events/events-presentations
2025-07-02 18:11:57,947 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/news-events/events-presentations
2025-07-02 18:11:57,947 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/news-events/events-presentations
2025-07-02 18:12:06,774 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/news-events/events-presentations, 内容长度: 180921
2025-07-02 18:12:06,832 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/news-events/events-presentations, 内容长度: 41465
2025-07-02 18:12:06,832 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 18:12:11,583 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 866
2025-07-02 18:12:11,583 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 18:12:11,583 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "xpath_rules": [
    "//div[@class='nir-widget--list']//article//a[@href]",
    "//div[@class='nir-widget--content']//article//a[@href]",
    "//div[@class='nir-widget--list']//div[contains(@class, 'field-nir-event-title')]//a[@href]",
    "//div[@class='nir-widget--list']//div[contains(@class, 'field-nir-document')]//a[@href]"
  ],
  "primary_xpath": "//div[@class='nir-widget--list']//article//a[@href]",
  "confidence": 0.90,
  "structure_analysis": "页面包含多个新闻和事件的列表，主要在具有类名'nir-widget--list'的div中。每个新闻项以article标签表示，链接在a标签中，包含href属性。日期信息通常在同一article内的其他div中。",
  "sample_links": [
    "https://edge.media-server.com/mmc/p/vqrjxogk",
    "https://edge.media-server.com/mmc/p/g6oxnay5",
    "https://edge.media-server.com/mmc/p/w7fp8yvn",
    "https://edge.media-server.com/mmc/p/2hmvqta7/",
    "https://edge.media-server.com/mmc/p/hvbgap5o"
  ]
}
```
2025-07-02 18:12:11,583 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 18:12:11,583 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/news-events/events-presentations 提取到 4 个XPath规则
2025-07-02 18:12:12,596 - src.core.search_research - INFO - 分析投资者关系页面: https://investors.trisaluslifesci.com/governance/governance-overview
2025-07-02 18:12:12,596 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/governance/governance-overview
2025-07-02 18:12:12,596 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/governance/governance-overview
2025-07-02 18:12:17,692 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /governance/governance-overview (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/governance/governance-overview
2025-07-02 18:12:18,699 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/governance/governance-overview
2025-07-02 18:12:23,794 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /governance/governance-overview (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/governance/governance-overview
2025-07-02 18:12:23,794 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/governance/governance-overview
2025-07-02 18:12:23,794 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/governance/governance-overview
2025-07-02 18:12:31,679 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/governance/governance-overview, 内容长度: 168988
2025-07-02 18:12:31,750 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/governance/governance-overview, 内容长度: 32468
2025-07-02 18:12:31,751 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 18:12:35,633 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 765
2025-07-02 18:12:35,633 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 18:12:35,633 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "xpath_rules": [
    "//div[contains(@class, 'nir-widget--list')]//a[@href]",
    "//article[contains(@class, 'node--nir-asset')]//a[@href]",
    "//div[@class='nir-widget--content']//a[@href]"
  ],
  "primary_xpath": "//div[contains(@class, 'nir-widget--list')]//a[@href]",
  "confidence": 0.90,
  "structure_analysis": "页面中包含多个新闻和公告的部分，主要在具有类名 'nir-widget--list' 的 div 中。每个新闻项以 article 标签表示，包含一个链接 a 标签，指向具体的新闻内容。该结构相对稳定，适合提取新闻链接。",
  "sample_links": [
    "/static-files/62569002-5a5a-4aac-a4d4-59932b437b9a",
    "/static-files/43e281ed-94d0-435e-90b7-ec78a928def7",
    "/static-files/77c4b799-285d-4ee1-b749-130e66699e3b",
    "/static-files/f4ae83c7-039a-49e3-84c0-9a7701085d6b",
    "/static-files/6268f9b7-fcf4-431a-b603-cf37bedffbe7"
  ]
}
```
2025-07-02 18:12:35,634 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 18:12:35,634 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/governance/governance-overview 提取到 3 个XPath规则
2025-07-02 18:12:36,647 - src.core.search_research - INFO - 分析投资者关系页面: https://investors.trisaluslifesci.com/financials/sec-filings
2025-07-02 18:12:36,648 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/financials/sec-filings
2025-07-02 18:12:36,648 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/financials/sec-filings
2025-07-02 18:12:41,750 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /financials/sec-filings (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/financials/sec-filings
2025-07-02 18:12:42,756 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/financials/sec-filings
2025-07-02 18:12:47,852 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /financials/sec-filings (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/financials/sec-filings
2025-07-02 18:12:47,852 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/financials/sec-filings
2025-07-02 18:12:47,853 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/financials/sec-filings
2025-07-02 18:12:55,719 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/financials/sec-filings, 内容长度: 201005
2025-07-02 18:12:55,798 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/financials/sec-filings, 内容长度: 58455
2025-07-02 18:12:55,799 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 18:13:00,060 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 738
2025-07-02 18:13:00,061 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 18:13:00,061 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "xpath_rules": [
    "//div[@class='view-content']//tr//td[2]//a[@href]",
    "//table[contains(@class, 'views-table')]//tr//td[2]//a",
    "//div[@class='views-element-container']//a[contains(@href, '/sec-filings/')]"
  ],
  "primary_xpath": "//div[@class='view-content']//tr//td[2]//a[@href]",
  "confidence": 0.90,
  "structure_analysis": "页面包含一个表格结构，新闻链接位于表格的第二列的a标签中。每个链接对应一个SEC文件的详细信息，且每个tr元素代表一条记录，包含日期、表单类型和描述。",
  "sample_links": [
    "/sec-filings/sec-filing/4/0001826667-25-000079",
    "/sec-filings/sec-filing/4/0001826667-25-000083",
    "/sec-filings/sec-filing/sc-i/0001641172-25-016156",
    "/sec-filings/sec-filing/8-k/0001826667-25-000077",
    "/sec-filings/sec-filing/s-4/0001641172-25-016155"
  ]
}
```
2025-07-02 18:13:00,061 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 18:13:00,061 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/financials/sec-filings 提取到 3 个XPath规则
2025-07-02 18:13:01,068 - src.core.search_research - INFO - 分析投资者关系页面: https://investors.trisaluslifesci.com/financials/quarterly-results
2025-07-02 18:13:01,068 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/financials/quarterly-results
2025-07-02 18:13:01,068 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/financials/quarterly-results
2025-07-02 18:13:06,158 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /financials/quarterly-results (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/financials/quarterly-results
2025-07-02 18:13:07,167 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/financials/quarterly-results
2025-07-02 18:13:12,267 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /financials/quarterly-results (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/financials/quarterly-results
2025-07-02 18:13:12,268 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/financials/quarterly-results
2025-07-02 18:13:12,268 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/financials/quarterly-results
2025-07-02 18:13:20,193 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/financials/quarterly-results, 内容长度: 169317
2025-07-02 18:13:20,262 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/financials/quarterly-results, 内容长度: 32939
2025-07-02 18:13:20,263 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 18:13:26,025 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 1210
2025-07-02 18:13:26,025 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 18:13:26,026 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "xpath_rules": [
    "//div[contains(@class, 'view-grouping')]//a[contains(@href, '/news-releases/')]",
    "//div[contains(@class, 'views-field-field-nir-bundle-content')]//a[@href]",
    "//div[@class='view-content']//a[@href]"
  ],
  "primary_xpath": "//div[contains(@class, 'view-grouping')]//a[contains(@href, '/news-releases/')]",
  "confidence": 0.90,
  "structure_analysis": "页面包含多个新闻发布的区域，主要在class为'view-grouping'的div中。每个新闻项的链接都在a标签内，且href属性包含'/news-releases/'，确保链接指向新闻发布。结构较为稳定，适合使用XPath进行提取。",
  "sample_links": [
    "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-reports-q4-and-full-year-2024-financial",
    "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-reports-q3-2024-financial-results-and-provides-business",
    "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-reports-q2-2024-financial-results-and-business-update",
    "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-reports-q1-2024-financial-results-and-business-update",
    "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-reports-q4-and-full-year-2023-financial-results-and"
  ]
}
```
2025-07-02 18:13:26,026 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 18:13:26,026 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/financials/quarterly-results 提取到 3 个XPath规则
2025-07-02 18:13:27,037 - src.core.search_research - INFO - 分析投资者关系页面: https://investors.trisaluslifesci.com/financials/annual-reports
2025-07-02 18:13:27,037 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/financials/annual-reports
2025-07-02 18:13:27,037 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/financials/annual-reports
2025-07-02 18:13:32,148 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /financials/annual-reports (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/financials/annual-reports
2025-07-02 18:13:33,151 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/financials/annual-reports
2025-07-02 18:13:38,242 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /financials/annual-reports (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/financials/annual-reports
2025-07-02 18:13:38,242 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/financials/annual-reports
2025-07-02 18:13:38,242 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/financials/annual-reports
2025-07-02 18:13:45,953 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/financials/annual-reports, 内容长度: 160080
2025-07-02 18:13:45,991 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/financials/annual-reports, 内容长度: 25122
2025-07-02 18:13:45,991 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 18:13:50,296 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 530
2025-07-02 18:13:50,296 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 18:13:50,296 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "xpath_rules": [
    "//div[@class='nir-widget--list']//a[@href]",
    "//div[@class='nir-widget--content']//a[@href]",
    "//section//a[contains(@href, '/news-events/')]"
  ],
  "primary_xpath": "//div[@class='nir-widget--list']//a[@href]",
  "confidence": 0.9,
  "structure_analysis": "页面中没有明确的新闻列表结构，新闻链接可能嵌套在多个div中，主要在类名为'nir-widget--list'的div中寻找链接。该结构可能会因内容更新而变化，因此需要定期验证XPath的有效性。",
  "sample_links": [
    "https://trisaluslifesci.com/news-and-events/",
    "https://trisaluslifesci.com/press-media/"
  ]
}
```
2025-07-02 18:13:50,297 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 18:13:50,297 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/financials/annual-reports 提取到 3 个XPath规则
2025-07-02 18:13:51,301 - src.core.search_research - INFO - 分析投资者关系页面: https://investors.trisaluslifesci.com/stock-information
2025-07-02 18:13:51,301 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/stock-information
2025-07-02 18:13:51,301 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/stock-information
2025-07-02 18:13:56,390 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /stock-information (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/stock-information
2025-07-02 18:13:57,404 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/stock-information
2025-07-02 18:14:02,483 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /stock-information (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/stock-information
2025-07-02 18:14:02,483 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/stock-information
2025-07-02 18:14:02,483 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/stock-information
2025-07-02 18:14:14,784 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/stock-information, 内容长度: 192293
2025-07-02 18:14:14,871 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/stock-information, 内容长度: 53836
2025-07-02 18:14:14,872 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 18:14:18,682 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 766
2025-07-02 18:14:18,682 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 18:14:18,682 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "xpath_rules": [
    "//ul[@class='navbar-nav navbar-secondary-links']//a[contains(@href, '/news-events')]",
    "//div[@class='block--nir-stock-chart']//a[contains(@href, '/news-events')]",
    "//nav[@id='block-secondarynavigation-2']//a[contains(@href, '/news-events')]"
  ],
  "primary_xpath": "//ul[@class='navbar-nav navbar-secondary-links']//a[contains(@href, '/news-events')]",
  "confidence": 0.90,
  "structure_analysis": "页面包含多个导航菜单和下拉列表，其中新闻链接主要集中在二级导航的新闻与事件部分。通过分析结构，发现新闻链接通常在包含新闻相关的ul或nav元素中，且链接的href属性包含'/news-events'。",
  "sample_links": [
    "https://trisaluslifesci.com/news-and-events/",
    "https://trisaluslifesci.com/news-and-events/press-releases",
    "https://trisaluslifesci.com/news-and-events/events-presentations"
  ]
}
```
2025-07-02 18:14:18,683 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 18:14:18,683 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/stock-information 提取到 3 个XPath规则
2025-07-02 18:14:19,692 - src.core.search_research - INFO - 分析投资者关系页面: https://investors.trisaluslifesci.com/investor-resources/email-alerts
2025-07-02 18:14:19,692 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/investor-resources/email-alerts
2025-07-02 18:14:19,692 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/investor-resources/email-alerts
2025-07-02 18:14:24,776 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /investor-resources/email-alerts (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/investor-resources/email-alerts
2025-07-02 18:14:25,790 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/investor-resources/email-alerts
2025-07-02 18:14:30,890 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /investor-resources/email-alerts (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/investor-resources/email-alerts
2025-07-02 18:14:30,890 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/investor-resources/email-alerts
2025-07-02 18:14:30,890 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/investor-resources/email-alerts
2025-07-02 18:14:39,750 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/investor-resources/email-alerts, 内容长度: 171319
2025-07-02 18:14:39,800 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/investor-resources/email-alerts, 内容长度: 34050
2025-07-02 18:14:39,801 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 18:14:43,755 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 740
2025-07-02 18:14:43,755 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 18:14:43,755 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "xpath_rules": [
    "//div[contains(@class, 'block--nir-email-alerts-signup-block')]//a[@href]",
    "//ul[contains(@class, 'navbar-secondary-links')]//a[contains(@href, '/news-and-events')]",
    "//ul[contains(@class, 'navbar-nav')]//a[contains(@href, '/news-events/press-releases')]"
  ],
  "primary_xpath": "//div[contains(@class, 'block--nir-email-alerts-signup-block')]//a[@href]",
  "confidence": 0.80,
  "structure_analysis": "页面中包含多个导航和内容区域，新闻链接主要集中在包含新闻和事件的下拉菜单中，以及特定的新闻发布区域。主要的新闻链接位于包含新闻列表的div中，且通常以a标签形式存在，href属性指向具体的新闻页面。",
  "sample_links": [
    "https://trisaluslifesci.com/news-and-events/",
    "https://trisaluslifesci.com/news-events/press-releases",
    "https://trisaluslifesci.com/press-media/"
  ]
}
```
2025-07-02 18:14:43,755 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 18:14:43,755 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/investor-resources/email-alerts 提取到 3 个XPath规则
2025-07-02 18:14:44,758 - src.core.search_research - INFO - 分析投资者关系页面: https://investors.trisaluslifesci.com/investor-resources/investor-contacts
2025-07-02 18:14:44,758 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/investor-resources/investor-contacts
2025-07-02 18:14:44,758 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/investor-resources/investor-contacts
2025-07-02 18:14:49,858 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /investor-resources/investor-contacts (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/investor-resources/investor-contacts
2025-07-02 18:14:50,860 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/investor-resources/investor-contacts
2025-07-02 18:14:55,959 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /investor-resources/investor-contacts (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/investor-resources/investor-contacts
2025-07-02 18:14:55,959 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/investor-resources/investor-contacts
2025-07-02 18:14:55,959 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/investor-resources/investor-contacts
2025-07-02 18:15:04,332 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/investor-resources/investor-contacts, 内容长度: 167996
2025-07-02 18:15:04,384 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/investor-resources/investor-contacts, 内容长度: 31601
2025-07-02 18:15:04,385 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 18:15:07,628 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 822
2025-07-02 18:15:07,628 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 18:15:07,628 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "xpath_rules": [
    "//ul[contains(@class, 'navbar-secondary-links')]//a[contains(@href, '/news-and-events/')]",
    "//ul[contains(@class, 'navbar-secondary-links')]//a[contains(@href, '/press-media/')]",
    "//a[contains(@href, '/news-events/press-releases')]",
    "//a[contains(@href, '/news-events/events-presentations')]"
  ],
  "primary_xpath": "//ul[contains(@class, 'navbar-secondary-links')]//a[contains(@href, '/news-and-events/')]",
  "confidence": 0.90,
  "structure_analysis": "页面中包含新闻链接的主要区域是二级导航栏，使用ul/li结构，链接元素为a标签，href属性指向新闻相关页面。",
  "sample_links": [
    "https://trisaluslifesci.com/news-and-events/",
    "https://trisaluslifesci.com/press-media/",
    "https://trisaluslifesci.com/news-events/press-releases",
    "https://trisaluslifesci.com/news-events/events-presentations"
  ]
}
```
2025-07-02 18:15:07,629 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 18:15:07,629 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/investor-resources/investor-contacts 提取到 4 个XPath规则
2025-07-02 18:15:08,637 - src.core.search_research - INFO - 总共提取到 30 个唯一XPath规则
2025-07-02 18:15:08,637 - src.core.search_research - INFO - 提取到XPath规则: 30个
2025-07-02 18:15:08,637 - src.core.search_research - INFO - 公司调研完成: TriSalus
2025-07-02 18:15:09,673 - src.services.drission_scraper - INFO - DrissionPage浏览器实例已关闭
2025-07-02 18:15:09,673 - src.services.web_scraper - INFO - DrissionPage资源已清理
2025-07-02 18:15:09,673 - __main__ - INFO - ✓ TriSalus 调研完成
2025-07-02 18:15:09,674 - __main__ - INFO - 调研结果已保存到: research_results.json
2025-07-02 18:15:09,674 - __main__ - INFO - 所有公司调研完成
2025-07-02 18:33:36,457 - __main__ - INFO - 开始HTML内容清理和分批处理优化测试
2025-07-02 18:33:36,458 - __main__ - INFO - 测试时间: 2025-07-02 18:33:36
2025-07-02 18:33:36,458 - __main__ - INFO - ============================================================
2025-07-02 18:33:36,458 - __main__ - INFO - 测试HTML内容清理功能
2025-07-02 18:33:36,458 - __main__ - INFO - ============================================================
2025-07-02 18:33:36,458 - src.services.web_scraper - INFO - 网页抓取服务初始化完成
2025-07-02 18:33:36,458 - __main__ - INFO - 原始HTML长度: 2,075 字符
2025-07-02 18:33:36,458 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 2,075 字符
2025-07-02 18:33:36,460 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-02 18:33:36,461 - src.services.web_scraper - INFO -   原始长度: 2,075 字符
2025-07-02 18:33:36,461 - src.services.web_scraper - INFO -   清理后长度: 841 字符
2025-07-02 18:33:36,461 - src.services.web_scraper - INFO -   减少比例: 59.5%
2025-07-02 18:33:36,461 - src.services.web_scraper - INFO -   清理统计: 移除1个标签, 3个属性, 1个注释, 0个空标签
2025-07-02 18:33:36,461 - __main__ - INFO - 清理后HTML长度: 841 字符
2025-07-02 18:33:36,461 - __main__ - INFO - 减少比例: 59.5%
2025-07-02 18:33:36,461 - __main__ - INFO - 清理后的HTML内容预览:
2025-07-02 18:33:36,461 - __main__ - INFO - ----------------------------------------
2025-07-02 18:33:36,461 - __main__ - INFO - <!DOCTYPE html><html><body><header class="main-header" id="header"><nav class="navigation"><ul><li><a href="/home">Home</a></li><li><a href="/about">About</a></li><li><a href="/investors">Investors</a></li></ul></nav></header><main class="content"><section class="news-section"><h1>Latest News</h1><div class="news-grid"><article class="news-item"><h2><a href="/news/1">News Title 1</a></h2><p>News content here...</p></article><article class="news-item"><h2><a href="/news/2">News Title 2</a></h2><p...
2025-07-02 18:33:36,461 - __main__ - INFO - ----------------------------------------
2025-07-02 18:33:36,461 - __main__ - INFO - ============================================================
2025-07-02 18:33:36,461 - __main__ - INFO - 测试HTML内容分割功能
2025-07-02 18:33:36,461 - __main__ - INFO - ============================================================
2025-07-02 18:33:36,462 - src.services.web_scraper - INFO - 网页抓取服务初始化完成
2025-07-02 18:33:36,462 - __main__ - INFO - 大HTML内容长度: 19,643 字符
2025-07-02 18:33:36,462 - src.services.web_scraper - INFO - HTML内容过长(19,643字符)，开始智能分割(阈值: 5,000)
2025-07-02 18:33:36,470 - src.services.web_scraper - INFO - 使用选择器 'section' 成功分割为 3 个分片
2025-07-02 18:33:36,470 - src.services.web_scraper - INFO - HTML内容分割完成，共生成 3 个分片
2025-07-02 18:33:36,470 - __main__ - INFO - 分割结果: 3 个分片
2025-07-02 18:33:36,471 - __main__ - INFO - 分片 1: 4,515 字符
2025-07-02 18:33:36,471 - __main__ - INFO - 分片 2: 4,563 字符
2025-07-02 18:33:36,471 - __main__ - INFO - 分片 3: 3,942 字符
2025-07-02 18:33:36,471 - __main__ - INFO - ============================================================
2025-07-02 18:33:36,471 - __main__ - INFO - 测试分批分析功能
2025-07-02 18:33:36,471 - __main__ - INFO - ============================================================
2025-07-02 18:33:36,511 - src.services.ai_analyzer - INFO - AI分析服务初始化完成
2025-07-02 18:33:36,511 - __main__ - INFO - 测试分片数量: 3
2025-07-02 18:33:36,511 - __main__ - INFO - 注意: 实际AI分析需要有效的OpenAI API密钥
2025-07-02 18:33:36,511 - __main__ - INFO - 当前仅测试分批处理的结构和逻辑
2025-07-02 18:33:36,511 - __main__ - INFO - 测试URL数量: 9
2025-07-02 18:33:36,511 - __main__ - INFO - 优化后URL数量: 3
2025-07-02 18:33:36,511 - __main__ - INFO - 优化后的URL:
2025-07-02 18:33:36,511 - __main__ - INFO -   - https://example.com/investors
2025-07-02 18:33:36,511 - __main__ - INFO -   - https://example.com/press-releases
2025-07-02 18:33:36,511 - __main__ - INFO -   - https://example.com/sec-filings
2025-07-02 18:33:36,511 - __main__ - INFO - 测试XPath规则数量: 8
2025-07-02 18:33:36,512 - __main__ - INFO - 优化后XPath规则数量: 3
2025-07-02 18:33:36,512 - __main__ - INFO - 优化后的XPath规则:
2025-07-02 18:33:36,512 - __main__ - INFO -   - //div[contains(@class, 'news')]//a[@href]
2025-07-02 18:33:36,512 - __main__ - INFO -   - //section[@class='press-releases']//a
2025-07-02 18:33:36,512 - __main__ - INFO -   - //ul[@class='nav']//a[contains(@href, '/news')]
2025-07-02 18:33:36,512 - __main__ - INFO - ============================================================
2025-07-02 18:33:36,512 - __main__ - INFO - 测试完整工作流程
2025-07-02 18:33:36,512 - __main__ - INFO - ============================================================
2025-07-02 18:33:36,512 - __main__ - INFO - 注意: 完整工作流程测试需要:
2025-07-02 18:33:36,512 - __main__ - INFO - 1. 有效的Google Search API密钥
2025-07-02 18:33:36,512 - __main__ - INFO - 2. 有效的OpenAI API密钥
2025-07-02 18:33:36,513 - __main__ - INFO - 3. 网络连接
2025-07-02 18:33:36,513 - __main__ - INFO - 当前仅测试代码结构和逻辑
2025-07-02 18:33:36,513 - src.services.google_search - INFO - Google搜索服务初始化完成
2025-07-02 18:33:36,513 - src.services.web_scraper - INFO - 网页抓取服务初始化完成
2025-07-02 18:33:36,518 - src.services.ai_analyzer - INFO - AI分析服务初始化完成
2025-07-02 18:33:36,518 - src.core.search_research - INFO - SearchResearchClass 初始化完成
2025-07-02 18:33:36,518 - __main__ - INFO - SearchResearchClass 初始化成功
2025-07-02 18:33:36,518 - __main__ - INFO - 所有组件初始化正常
2025-07-02 18:33:36,519 - __main__ - INFO - ============================================================
2025-07-02 18:33:36,519 - __main__ - INFO - 所有测试完成
2025-07-02 18:33:36,519 - __main__ - INFO - ============================================================
2025-07-03 04:39:20,372 - __main__ - INFO - AI Tools Find News - 公司调研工具启动
2025-07-03 04:39:20,373 - __main__ - INFO - 准备调研 1 个公司
2025-07-03 04:39:20,373 - __main__ - INFO - 开始调研第 1/1 个公司: TriSalus
2025-07-03 04:39:20,373 - src.services.google_search - INFO - Google搜索服务初始化完成
2025-07-03 04:39:20,373 - src.services.web_scraper - INFO - 网页抓取服务初始化完成
2025-07-03 04:39:20,432 - src.services.ai_analyzer - INFO - AI分析服务初始化完成
2025-07-03 04:39:20,432 - src.core.search_research - INFO - SearchResearchClass 初始化完成
2025-07-03 04:39:20,432 - src.core.search_research - INFO - 开始调研公司: TriSalus
2025-07-03 04:39:20,436 - src.core.search_research - INFO - 步骤1: 搜索公司官网
2025-07-03 04:39:20,436 - src.services.google_search - INFO - 执行Google搜索: TriSalus
2025-07-03 04:39:23,168 - src.services.google_search - INFO - 搜索成功，返回 10 个结果
2025-07-03 04:39:23,169 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 04:39:25,818 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 264
2025-07-03 04:39:25,819 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 04:39:25,819 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "official_website": "https://trisaluslifesci.com/",
  "confidence": 0.98,
  "reasoning": "选择了https://trisaluslifesci.com/作为TriSalus的官方网站，因为该URL包含公司名称，并且是以.com结尾的主流域名。该页面的标题为'Trisalus Life Sciences'，直接表明其与公司相关。此外，其他搜索结果的URL均为子页面或投资者相关页面，不符合首页的要求。"
}
```
2025-07-03 04:39:25,819 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 04:39:25,819 - src.core.search_research - INFO - 找到官网: https://trisaluslifesci.com/
2025-07-03 04:39:25,819 - src.core.search_research - INFO - 步骤2: 分析官网首页
2025-07-03 04:39:25,819 - src.services.web_scraper - INFO - 开始抓取页面: https://trisaluslifesci.com/
2025-07-03 04:39:25,819 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://trisaluslifesci.com/
2025-07-03 04:39:26,011 - src.services.web_scraper - WARNING - requests页面抓取失败，状态码: 403, URL: https://trisaluslifesci.com/
2025-07-03 04:39:27,012 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://trisaluslifesci.com/
2025-07-03 04:39:27,151 - src.services.web_scraper - WARNING - requests页面抓取失败，状态码: 403, URL: https://trisaluslifesci.com/
2025-07-03 04:39:27,151 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://trisaluslifesci.com/
2025-07-03 04:39:27,497 - src.services.drission_scraper - INFO - DrissionPage抓取服务初始化完成
2025-07-03 04:39:27,497 - src.services.web_scraper - INFO - DrissionPage抓取服务已初始化
2025-07-03 04:39:27,497 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://trisaluslifesci.com/
2025-07-03 04:39:28,401 - src.services.drission_scraper - INFO - ChromiumPage实例创建成功
2025-07-03 04:39:37,618 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://trisaluslifesci.com/, 内容长度: 276703
2025-07-03 04:39:37,618 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 276,703 字符
2025-07-03 04:39:37,949 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 04:39:37,949 - src.services.web_scraper - INFO -   原始长度: 276,703 字符
2025-07-03 04:39:37,949 - src.services.web_scraper - INFO -   清理后长度: 131,597 字符
2025-07-03 04:39:37,949 - src.services.web_scraper - INFO -   减少比例: 52.4%
2025-07-03 04:39:37,949 - src.services.web_scraper - INFO -   清理统计: 移除53个标签, 1247个属性, 2个注释, 216个空标签
2025-07-03 04:39:37,949 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://trisaluslifesci.com/, 内容长度: 131597
2025-07-03 04:39:37,951 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 131,597 字符
2025-07-03 04:39:38,152 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 04:39:38,152 - src.services.web_scraper - INFO -   原始长度: 131,597 字符
2025-07-03 04:39:38,153 - src.services.web_scraper - INFO -   清理后长度: 130,577 字符
2025-07-03 04:39:38,153 - src.services.web_scraper - INFO -   减少比例: 0.8%
2025-07-03 04:39:38,153 - src.services.web_scraper - INFO -   清理统计: 移除0个标签, 0个属性, 0个注释, 9个空标签
2025-07-03 04:39:38,153 - src.core.search_research - INFO - HTML内容过长(130,577字符)，使用分批处理
2025-07-03 04:39:38,153 - src.services.web_scraper - INFO - HTML内容过长(130,577字符)，开始智能分割(阈值: 50,000)
2025-07-03 04:39:38,251 - src.services.web_scraper - INFO - 使用选择器 'section' 成功分割为 4 个分片
2025-07-03 04:39:38,251 - src.services.web_scraper - INFO - HTML内容分割完成，共生成 4 个分片
2025-07-03 04:39:38,251 - src.services.ai_analyzer - INFO - 开始分批分析投资者关系页面，共 4 个分片
2025-07-03 04:39:38,251 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 04:39:42,334 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 720
2025-07-03 04:39:42,334 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 04:39:42,335 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_relations_urls": [
    "https://investors.trisaluslifesci.com/investor-relations",
    "https://investors.trisaluslifesci.com/news-events/press-releases",
    "https://investors.trisaluslifesci.com/governance/governance-overview",
    "https://investors.trisaluslifesci.com/financials/sec-filings",
    "https://investors.trisaluslifesci.com/financials/annual-reports",
    "https://investors.trisaluslifesci.com/stock-information",
    "https://investors.trisaluslifesci.com/investor-resources/email-alerts"
  ],
  "found_keywords": ["投资者关系", "投资者", "财务信息", "年报", "公告"],
  "confidence": 0.95,
  "analysis_notes": "在分析过程中，识别了多个包含投资者关系相关关键词的链接。所有链接均为绝对链接，且去除了重复项。关键词包括中文和英文表述，确保覆盖了投资者关系的相关内容。"
}
```
2025-07-03 04:39:42,335 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 04:39:42,335 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 04:39:46,157 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 708
2025-07-03 04:39:46,157 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 04:39:46,157 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_relations_urls": [
    "https://investors.trisaluslifesci.com/",
    "https://investors.trisaluslifesci.com/news-events/press-releases",
    "https://investors.trisaluslifesci.com/governance/governance-overview",
    "https://investors.trisaluslifesci.com/financials/sec-filings",
    "https://investors.trisaluslifesci.com/financials/annual-reports",
    "https://investors.trisaluslifesci.com/stock-information",
    "https://investors.trisaluslifesci.com/investor-resources/email-alerts"
  ],
  "found_keywords": ["投资者", "财务信息", "年报", "公告"],
  "confidence": 0.95,
  "analysis_notes": "在分析过程中，识别了多个与投资者关系相关的链接，主要集中在导航菜单中。所有链接均为绝对链接，且没有重复。关键词包括'投资者'、'财务信息'、'年报'等，表明这些链接与投资者关系相关。"
}
```
2025-07-03 04:39:46,157 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 04:39:46,157 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 04:39:51,509 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 817
2025-07-03 04:39:51,509 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 04:39:51,509 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_relations_urls": [
    "https://trisaluslifesci.com/investors/",
    "https://investors.trisaluslifesci.com/",
    "https://investors.trisaluslifesci.com/news-events/press-releases",
    "https://investors.trisaluslifesci.com/governance/governance-overview",
    "https://investors.trisaluslifesci.com/financials/sec-filings",
    "https://investors.trisaluslifesci.com/financials/annual-reports",
    "https://investors.trisaluslifesci.com/stock-information",
    "https://investors.trisaluslifesci.com/investor-resources/email-alerts"
  ],
  "found_keywords": ["投资者关系", "投资者", "财务信息", "年报", "公告"],
  "confidence": 0.95,
  "analysis_notes": "在分析过程中，首先识别了包含'投资者关系'、'投资者'、'IR'、'Investor Relations'等关键词的链接。然后，检查了HTML内容中的导航菜单和主要内容区域，发现多个与投资者关系相关的链接。所有相对链接均已转换为绝对链接，并去除了重复项。最终确认了多个相关链接，置信度较高。"
}
```
2025-07-03 04:39:51,509 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 04:39:51,510 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 04:39:52,966 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 188
2025-07-03 04:39:52,966 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 04:39:52,966 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_relations_urls": [],
  "found_keywords": [],
  "confidence": 0.0,
  "analysis_notes": "在提供的HTML内容中，没有找到任何与投资者关系相关的链接或关键词。内容主要集中在公司的产品管线，没有涉及投资者关系、财务信息或相关公告等主题。"
}
```
2025-07-03 04:39:52,966 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 04:39:52,967 - src.services.ai_analyzer - INFO - 分批分析完成: 22 -> 9 个唯一URL，平均置信度: 0.95
2025-07-03 04:39:52,967 - src.core.search_research - INFO - 分批处理结果: {'total_fragments': 4, 'valid_results': 3, 'total_urls_found': 22, 'unique_urls': 9}
2025-07-03 04:39:52,967 - src.core.search_research - INFO - 找到投资者关系页面: 9个
2025-07-03 04:39:52,967 - src.core.search_research - INFO - 步骤3: 分析投资者关系页面
2025-07-03 04:39:52,967 - src.core.search_research - INFO - 分析投资者关系页面: https://investors.trisaluslifesci.com/investor-relations
2025-07-03 04:39:52,967 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/investor-relations
2025-07-03 04:39:52,967 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/investor-relations
2025-07-03 04:39:58,291 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /investor-relations (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/investor-relations
2025-07-03 04:39:59,299 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/investor-relations
2025-07-03 04:40:04,390 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /investor-relations (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/investor-relations
2025-07-03 04:40:04,390 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/investor-relations
2025-07-03 04:40:04,390 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/investor-relations
2025-07-03 04:40:15,313 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/investor-relations, 内容长度: 188727
2025-07-03 04:40:15,313 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 188,727 字符
2025-07-03 04:40:15,451 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 04:40:15,451 - src.services.web_scraper - INFO -   原始长度: 188,727 字符
2025-07-03 04:40:15,451 - src.services.web_scraper - INFO -   清理后长度: 33,470 字符
2025-07-03 04:40:15,451 - src.services.web_scraper - INFO -   减少比例: 82.3%
2025-07-03 04:40:15,451 - src.services.web_scraper - INFO -   清理统计: 移除16个标签, 89个属性, 13个注释, 34个空标签
2025-07-03 04:40:15,451 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/investor-relations, 内容长度: 33470
2025-07-03 04:40:15,451 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 33,470 字符
2025-07-03 04:40:15,520 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 04:40:15,521 - src.services.web_scraper - INFO -   原始长度: 33,470 字符
2025-07-03 04:40:15,521 - src.services.web_scraper - INFO -   清理后长度: 32,991 字符
2025-07-03 04:40:15,521 - src.services.web_scraper - INFO -   减少比例: 1.4%
2025-07-03 04:40:15,521 - src.services.web_scraper - INFO -   清理统计: 移除0个标签, 0个属性, 0个注释, 3个空标签
2025-07-03 04:40:15,521 - src.core.search_research - INFO - 页面内容适中(32,991字符)，使用单次处理: https://investors.trisaluslifesci.com/investor-relations
2025-07-03 04:40:15,521 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 04:40:19,721 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 816
2025-07-03 04:40:19,721 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 04:40:19,721 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "xpath_rules": [
    "//div[contains(@class, 'block--news-grid')]//a[contains(@href, '/news-releases/')]",
    "//div[contains(@class, 'nir-widget--list')]//a[@href]",
    "//div[@class='c-press-feed']//a[@href]"
  ],
  "primary_xpath": "//div[contains(@class, 'block--news-grid')]//a[contains(@href, '/news-releases/')]",
  "confidence": 0.90,
  "structure_analysis": "页面中新闻列表位于class为'block--news-grid'的div中，包含多个新闻项，每个新闻项由一个包含链接的a标签组成。每个新闻项还包含日期信息，确保提取的链接与新闻标题相关。",
  "sample_links": [
    "/news-releases/news-release-details/trisalus-life-sciences-announces-commencement-exchange-offer-0",
    "/news-releases/news-release-details/trisalus-life-sciences-announces-launch-trinav-flx-infusion",
    "/news-releases/news-release-details/trisalus-life-sciences-announces-chief-financial-officer"
  ]
}
```
2025-07-03 04:40:19,721 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 04:40:19,721 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/investor-relations 提取到 82 个主要XPath，3 个其他规则
2025-07-03 04:40:20,735 - src.core.search_research - INFO - 分析投资者关系页面: https://investors.trisaluslifesci.com/news-events/press-releases
2025-07-03 04:40:20,735 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/news-events/press-releases
2025-07-03 04:40:20,737 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/news-events/press-releases
2025-07-03 04:40:25,824 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /news-events/press-releases (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/news-events/press-releases
2025-07-03 04:40:26,831 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/news-events/press-releases
2025-07-03 04:40:31,925 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /news-events/press-releases (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/news-events/press-releases
2025-07-03 04:40:31,926 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/news-events/press-releases
2025-07-03 04:40:31,926 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/news-events/press-releases
2025-07-03 04:40:40,471 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/news-events/press-releases, 内容长度: 194151
2025-07-03 04:40:40,471 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 194,151 字符
2025-07-03 04:40:40,641 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 04:40:40,641 - src.services.web_scraper - INFO -   原始长度: 194,151 字符
2025-07-03 04:40:40,641 - src.services.web_scraper - INFO -   清理后长度: 35,090 字符
2025-07-03 04:40:40,641 - src.services.web_scraper - INFO -   减少比例: 81.9%
2025-07-03 04:40:40,641 - src.services.web_scraper - INFO -   清理统计: 移除9个标签, 112个属性, 6个注释, 27个空标签
2025-07-03 04:40:40,641 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/news-events/press-releases, 内容长度: 35090
2025-07-03 04:40:40,641 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 35,090 字符
2025-07-03 04:40:40,718 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 04:40:40,718 - src.services.web_scraper - INFO -   原始长度: 35,090 字符
2025-07-03 04:40:40,718 - src.services.web_scraper - INFO -   清理后长度: 34,611 字符
2025-07-03 04:40:40,718 - src.services.web_scraper - INFO -   减少比例: 1.4%
2025-07-03 04:40:40,718 - src.services.web_scraper - INFO -   清理统计: 移除0个标签, 0个属性, 0个注释, 3个空标签
2025-07-03 04:40:40,718 - src.core.search_research - INFO - 页面内容适中(34,611字符)，使用单次处理: https://investors.trisaluslifesci.com/news-events/press-releases
2025-07-03 04:40:40,718 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 04:40:53,732 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 1165
2025-07-03 04:40:53,732 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 04:40:53,733 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "xpath_rules": [
    "//div[contains(@class, 'block--news-list-starter')]//a[@href]",
    "//article[contains(@class, 'node--nir-news')]//a[@href]",
    "//div[@class='nir-widget--list']//a[@href]"
  ],
  "primary_xpath": "//div[contains(@class, 'block--news-list-starter')]//a[@href]",
  "confidence": 0.90,
  "structure_analysis": "页面包含一个主要的新闻列表区域，使用了多个嵌套的div和article元素。新闻链接位于包含新闻标题的a标签中，且这些a标签都在特定的类名下，确保了选择的准确性。",
  "sample_links": [
    "https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-commencement-exchange-offer-0",
    "https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-launch-trinav-flx-infusion",
    "https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-chief-financial-officer",
    "https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-reports-first-quarter-2025-results-and",
    "https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-host-first-quarter-2025-financial-results"
  ]
}
```
2025-07-03 04:40:53,733 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 04:40:53,733 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/news-events/press-releases 提取到 61 个主要XPath，3 个其他规则
2025-07-03 04:40:54,743 - src.core.search_research - INFO - 分析投资者关系页面: https://investors.trisaluslifesci.com/governance/governance-overview
2025-07-03 04:40:54,743 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/governance/governance-overview
2025-07-03 04:40:54,743 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/governance/governance-overview
2025-07-03 04:40:59,812 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /governance/governance-overview (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/governance/governance-overview
2025-07-03 04:41:00,812 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/governance/governance-overview
2025-07-03 04:41:05,915 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /governance/governance-overview (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/governance/governance-overview
2025-07-03 04:41:05,915 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/governance/governance-overview
2025-07-03 04:41:05,915 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/governance/governance-overview
2025-07-03 04:41:14,242 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/governance/governance-overview, 内容长度: 168988
2025-07-03 04:41:14,242 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 168,988 字符
2025-07-03 04:41:14,339 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 04:41:14,339 - src.services.web_scraper - INFO -   原始长度: 168,988 字符
2025-07-03 04:41:14,339 - src.services.web_scraper - INFO -   清理后长度: 22,278 字符
2025-07-03 04:41:14,339 - src.services.web_scraper - INFO -   减少比例: 86.8%
2025-07-03 04:41:14,339 - src.services.web_scraper - INFO -   清理统计: 移除8个标签, 74个属性, 6个注释, 23个空标签
2025-07-03 04:41:14,339 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/governance/governance-overview, 内容长度: 22278
2025-07-03 04:41:14,339 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 22,278 字符
2025-07-03 04:41:14,387 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 04:41:14,387 - src.services.web_scraper - INFO -   原始长度: 22,278 字符
2025-07-03 04:41:14,388 - src.services.web_scraper - INFO -   清理后长度: 21,799 字符
2025-07-03 04:41:14,388 - src.services.web_scraper - INFO -   减少比例: 2.2%
2025-07-03 04:41:14,388 - src.services.web_scraper - INFO -   清理统计: 移除0个标签, 0个属性, 0个注释, 3个空标签
2025-07-03 04:41:14,388 - src.core.search_research - INFO - 页面内容适中(21,799字符)，使用单次处理: https://investors.trisaluslifesci.com/governance/governance-overview
2025-07-03 04:41:14,388 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 04:41:19,026 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 778
2025-07-03 04:41:19,027 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 04:41:19,027 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "xpath_rules": [
    "//div[contains(@class, 'nir-widget--list')]//a[@href]",
    "//div[@class='nir-widget llf-container-fluid-full']//a[@href]",
    "//article[contains(@class, 'node--nir-asset')]//a[@href]"
  ],
  "primary_xpath": "//div[contains(@class, 'nir-widget--list')]//a[@href]",
  "confidence": 0.90,
  "structure_analysis": "页面包含多个区域，其中新闻链接主要位于包含类名为'nir-widget--list'的div中。每个新闻链接都在一个a标签内，且这些链接通常指向PDF文件或其他相关文档。页面结构较为稳定，主要依赖于类名和标签结构。",
  "sample_links": [
    "/static-files/62569002-5a5a-4aac-a4d4-59932b437b9a",
    "/static-files/43e281ed-94d0-435e-90b7-ec78a928def7",
    "/static-files/77c4b799-285d-4ee1-b749-130e66699e3b",
    "/static-files/f4ae83c7-039a-49e3-84c0-9a7701085d6b",
    "/static-files/6268f9b7-fcf4-431a-b603-cf37bedffbe7"
  ]
}
```
2025-07-03 04:41:19,027 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 04:41:19,027 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/governance/governance-overview 提取到 53 个主要XPath，3 个其他规则
2025-07-03 04:41:20,037 - src.core.search_research - INFO - 分析投资者关系页面: https://investors.trisaluslifesci.com/financials/sec-filings
2025-07-03 04:41:20,037 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/financials/sec-filings
2025-07-03 04:41:20,037 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/financials/sec-filings
2025-07-03 04:41:25,107 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /financials/sec-filings (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/financials/sec-filings
2025-07-03 04:41:26,118 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/financials/sec-filings
2025-07-03 04:41:31,216 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /financials/sec-filings (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/financials/sec-filings
2025-07-03 04:41:31,216 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/financials/sec-filings
2025-07-03 04:41:31,216 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/financials/sec-filings
2025-07-03 04:41:39,114 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/financials/sec-filings, 内容长度: 201005
2025-07-03 04:41:39,114 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 201,005 字符
2025-07-03 04:41:39,285 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 04:41:39,285 - src.services.web_scraper - INFO -   原始长度: 201,005 字符
2025-07-03 04:41:39,285 - src.services.web_scraper - INFO -   清理后长度: 36,893 字符
2025-07-03 04:41:39,285 - src.services.web_scraper - INFO -   减少比例: 81.6%
2025-07-03 04:41:39,285 - src.services.web_scraper - INFO -   清理统计: 移除8个标签, 139个属性, 6个注释, 52个空标签
2025-07-03 04:41:39,285 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/financials/sec-filings, 内容长度: 36893
2025-07-03 04:41:39,285 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 36,893 字符
2025-07-03 04:41:39,381 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 04:41:39,382 - src.services.web_scraper - INFO -   原始长度: 36,893 字符
2025-07-03 04:41:39,382 - src.services.web_scraper - INFO -   清理后长度: 36,414 字符
2025-07-03 04:41:39,382 - src.services.web_scraper - INFO -   减少比例: 1.3%
2025-07-03 04:41:39,382 - src.services.web_scraper - INFO -   清理统计: 移除0个标签, 0个属性, 0个注释, 3个空标签
2025-07-03 04:41:39,382 - src.core.search_research - INFO - 页面内容适中(36,414字符)，使用单次处理: https://investors.trisaluslifesci.com/financials/sec-filings
2025-07-03 04:41:39,382 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 04:41:45,633 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 1024
2025-07-03 04:41:45,633 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 04:41:45,633 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "xpath_rules": [
    "//div[@class='view-content']//table//tr//td[contains(@class, 'views-field-field-nir-sec-form')]//a[@href]",
    "//div[@class='view-content']//table//tr//td[contains(@class, 'views-field-field-nir-sec-description')]//a[@href]",
    "//div[@class='view-content']//table//tr//td[contains(@class, 'views-field-field-nir-sec-date-filed')]//following-sibling::td[contains(@class, 'views-field-field-nir-sec-form')]//a[@href]"
  ],
  "primary_xpath": "//div[@class='view-content']//table//tr//td[contains(@class, 'views-field-field-nir-sec-form')]//a[@href]",
  "confidence": 0.90,
  "structure_analysis": "页面包含一个表格结构，新闻链接位于表格的特定单元格中。每个新闻项由日期、表单类型和描述组成，链接位于表单类型或描述的单元格内。该结构相对稳定，适合使用XPath进行提取。",
  "sample_links": [
    "/sec-filings/sec-filing/4/0001826667-25-000079",
    "/sec-filings/sec-filing/4/0001826667-25-000083",
    "/sec-filings/sec-filing/4/0001826667-25-000081",
    "/sec-filings/sec-filing/4/0001826667-25-000087",
    "/sec-filings/sec-filing/sc-i/0001641172-25-016156"
  ]
}
```
2025-07-03 04:41:45,633 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 04:41:45,633 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/financials/sec-filings 提取到 105 个主要XPath，3 个其他规则
2025-07-03 04:41:46,646 - src.core.search_research - INFO - 分析投资者关系页面: https://investors.trisaluslifesci.com/financials/annual-reports
2025-07-03 04:41:46,646 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/financials/annual-reports
2025-07-03 04:41:46,646 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/financials/annual-reports
2025-07-03 04:41:51,727 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /financials/annual-reports (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/financials/annual-reports
2025-07-03 04:41:52,735 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/financials/annual-reports
2025-07-03 04:41:57,831 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /financials/annual-reports (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/financials/annual-reports
2025-07-03 04:41:57,831 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/financials/annual-reports
2025-07-03 04:41:57,831 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/financials/annual-reports
2025-07-03 04:42:05,612 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/financials/annual-reports, 内容长度: 160080
2025-07-03 04:42:05,613 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 160,080 字符
2025-07-03 04:42:05,687 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 04:42:05,687 - src.services.web_scraper - INFO -   原始长度: 160,080 字符
2025-07-03 04:42:05,687 - src.services.web_scraper - INFO -   清理后长度: 15,826 字符
2025-07-03 04:42:05,687 - src.services.web_scraper - INFO -   减少比例: 90.1%
2025-07-03 04:42:05,687 - src.services.web_scraper - INFO -   清理统计: 移除8个标签, 68个属性, 6个注释, 16个空标签
2025-07-03 04:42:05,687 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/financials/annual-reports, 内容长度: 15826
2025-07-03 04:42:05,687 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 15,826 字符
2025-07-03 04:42:05,725 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 04:42:05,725 - src.services.web_scraper - INFO -   原始长度: 15,826 字符
2025-07-03 04:42:05,725 - src.services.web_scraper - INFO -   清理后长度: 15,347 字符
2025-07-03 04:42:05,725 - src.services.web_scraper - INFO -   减少比例: 3.0%
2025-07-03 04:42:05,725 - src.services.web_scraper - INFO -   清理统计: 移除0个标签, 0个属性, 0个注释, 3个空标签
2025-07-03 04:42:05,725 - src.core.search_research - INFO - 页面内容适中(15,347字符)，使用单次处理: https://investors.trisaluslifesci.com/financials/annual-reports
2025-07-03 04:42:05,726 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 04:42:09,204 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 637
2025-07-03 04:42:09,204 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 04:42:09,204 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "xpath_rules": [
    "//div[contains(@class, 'block--nir-assets')]//a[@href]",
    "//div[@id='lfg-main-content']//a[contains(@href, '/news-events/press-releases')]",
    "//section//a[contains(@href, '/news-events/')]"
  ],
  "primary_xpath": "//div[contains(@class, 'block--nir-assets')]//a[@href]",
  "confidence": 0.80,
  "structure_analysis": "页面中包含新闻链接的区域主要在一个包含类名为 'block--nir-assets' 的 div 中，链接元素为 a 标签，通常包含 href 属性指向新闻或公告的具体页面。该结构可能会因内容更新而变化，但主要的容器结构保持稳定。",
  "sample_links": [
    "https://trisaluslifesci.com/news-events/press-releases",
    "https://trisaluslifesci.com/news-events/events-presentations"
  ]
}
```
2025-07-03 04:42:09,204 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 04:42:09,204 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/financials/annual-reports 提取到 54 个主要XPath，3 个其他规则
2025-07-03 04:42:10,215 - src.core.search_research - INFO - 分析投资者关系页面: https://investors.trisaluslifesci.com/stock-information
2025-07-03 04:42:10,215 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/stock-information
2025-07-03 04:42:10,215 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/stock-information
2025-07-03 04:42:15,332 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /stock-information (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/stock-information
2025-07-03 04:42:16,334 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/stock-information
2025-07-03 04:42:21,406 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /stock-information (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/stock-information
2025-07-03 04:42:21,406 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/stock-information
2025-07-03 04:42:21,406 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/stock-information
2025-07-03 04:42:34,287 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/stock-information, 内容长度: 193189
2025-07-03 04:42:34,287 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 193,189 字符
2025-07-03 04:42:34,468 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 04:42:34,468 - src.services.web_scraper - INFO -   原始长度: 193,189 字符
2025-07-03 04:42:34,468 - src.services.web_scraper - INFO -   清理后长度: 33,124 字符
2025-07-03 04:42:34,468 - src.services.web_scraper - INFO -   减少比例: 82.9%
2025-07-03 04:42:34,468 - src.services.web_scraper - INFO -   清理统计: 移除10个标签, 148个属性, 6个注释, 48个空标签
2025-07-03 04:42:34,468 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/stock-information, 内容长度: 33124
2025-07-03 04:42:34,469 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 33,124 字符
2025-07-03 04:42:34,582 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 04:42:34,582 - src.services.web_scraper - INFO -   原始长度: 33,124 字符
2025-07-03 04:42:34,582 - src.services.web_scraper - INFO -   清理后长度: 32,645 字符
2025-07-03 04:42:34,582 - src.services.web_scraper - INFO -   减少比例: 1.4%
2025-07-03 04:42:34,582 - src.services.web_scraper - INFO -   清理统计: 移除0个标签, 0个属性, 0个注释, 3个空标签
2025-07-03 04:42:34,582 - src.core.search_research - INFO - 页面内容适中(32,645字符)，使用单次处理: https://investors.trisaluslifesci.com/stock-information
2025-07-03 04:42:34,582 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 04:42:38,631 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 762
2025-07-03 04:42:38,631 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 04:42:38,631 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "xpath_rules": [
    "//div[contains(@class, 'region-content')]//a[contains(@href, '/news-events/press-releases')]",
    "//ul[contains(@class, 'navbar-secondary-links')]//a[contains(@href, '/news-and-events')]",
    "//div[@class='block--market-data-block__historical-price-lookup']//a[@href]"
  ],
  "primary_xpath": "//div[contains(@class, 'region-content')]//a[contains(@href, '/news-events/press-releases')]",
  "confidence": 0.90,
  "structure_analysis": "页面包含多个区域，其中新闻链接主要集中在'新闻与事件'部分，使用了div和ul结构。新闻链接通常在a标签中，包含href属性，且可能伴随标题和日期信息。",
  "sample_links": [
    "https://trisaluslifesci.com/news-events/press-releases",
    "https://trisaluslifesci.com/news-and-events",
    "https://trisaluslifesci.com/news-events/events-presentations"
  ]
}
```
2025-07-03 04:42:38,631 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 04:42:38,631 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/stock-information 提取到 92 个主要XPath，3 个其他规则
2025-07-03 04:42:39,645 - src.core.search_research - INFO - 分析投资者关系页面: https://investors.trisaluslifesci.com/investor-resources/email-alerts
2025-07-03 04:42:39,645 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/investor-resources/email-alerts
2025-07-03 04:42:39,645 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/investor-resources/email-alerts
2025-07-03 04:42:44,750 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /investor-resources/email-alerts (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/investor-resources/email-alerts
2025-07-03 04:42:45,751 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/investor-resources/email-alerts
2025-07-03 04:42:50,842 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /investor-resources/email-alerts (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/investor-resources/email-alerts
2025-07-03 04:42:50,842 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/investor-resources/email-alerts
2025-07-03 04:42:50,842 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/investor-resources/email-alerts
2025-07-03 04:42:59,668 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/investor-resources/email-alerts, 内容长度: 171319
2025-07-03 04:42:59,668 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 171,319 字符
2025-07-03 04:42:59,762 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 04:42:59,762 - src.services.web_scraper - INFO -   原始长度: 171,319 字符
2025-07-03 04:42:59,762 - src.services.web_scraper - INFO -   清理后长度: 20,872 字符
2025-07-03 04:42:59,762 - src.services.web_scraper - INFO -   减少比例: 87.8%
2025-07-03 04:42:59,762 - src.services.web_scraper - INFO -   清理统计: 移除11个标签, 103个属性, 6个注释, 20个空标签
2025-07-03 04:42:59,762 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/investor-resources/email-alerts, 内容长度: 20872
2025-07-03 04:42:59,762 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 20,872 字符
2025-07-03 04:42:59,819 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 04:42:59,819 - src.services.web_scraper - INFO -   原始长度: 20,872 字符
2025-07-03 04:42:59,819 - src.services.web_scraper - INFO -   清理后长度: 20,393 字符
2025-07-03 04:42:59,819 - src.services.web_scraper - INFO -   减少比例: 2.3%
2025-07-03 04:42:59,819 - src.services.web_scraper - INFO -   清理统计: 移除0个标签, 0个属性, 0个注释, 3个空标签
2025-07-03 04:42:59,819 - src.core.search_research - INFO - 页面内容适中(20,393字符)，使用单次处理: https://investors.trisaluslifesci.com/investor-resources/email-alerts
2025-07-03 04:42:59,819 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 04:43:06,797 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 675
2025-07-03 04:43:06,798 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 04:43:06,798 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "xpath_rules": [
    "//div[contains(@class, 'block--nir-email-alerts-signup-block')]//a[@href]",
    "//ul[contains(@class, 'navbar-secondary-links')]//a[contains(text(), 'Press Releases')]",
    "//div[@class='region region-content']//article//a[@href]"
  ],
  "primary_xpath": "//div[contains(@class, 'block--nir-email-alerts-signup-block')]//a[@href]",
  "confidence": 0.80,
  "structure_analysis": "页面包含多个导航和内容区域，新闻链接主要集中在包含新闻和事件的部分。主要的新闻链接位于包含新闻发布的div中，且通常以a标签形式存在。页面结构较为复杂，可能会有变化，但主要的新闻链接位置相对稳定。",
  "sample_links": [
    "https://trisaluslifesci.com/news-events/press-releases",
    "https://trisaluslifesci.com/news-events/events-presentations"
  ]
}
```
2025-07-03 04:43:06,798 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 04:43:06,798 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/investor-resources/email-alerts 提取到 73 个主要XPath，3 个其他规则
2025-07-03 04:43:07,809 - src.core.search_research - INFO - 分析投资者关系页面: https://investors.trisaluslifesci.com/
2025-07-03 04:43:07,809 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/
2025-07-03 04:43:07,809 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/
2025-07-03 04:43:12,889 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: / (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/
2025-07-03 04:43:13,895 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/
2025-07-03 04:43:18,978 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: / (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/
2025-07-03 04:43:18,978 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/
2025-07-03 04:43:18,978 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/
2025-07-03 04:43:27,163 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/, 内容长度: 188326
2025-07-03 04:43:27,163 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 188,326 字符
2025-07-03 04:43:27,308 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 04:43:27,309 - src.services.web_scraper - INFO -   原始长度: 188,326 字符
2025-07-03 04:43:27,309 - src.services.web_scraper - INFO -   清理后长度: 33,470 字符
2025-07-03 04:43:27,309 - src.services.web_scraper - INFO -   减少比例: 82.2%
2025-07-03 04:43:27,309 - src.services.web_scraper - INFO -   清理统计: 移除15个标签, 89个属性, 13个注释, 34个空标签
2025-07-03 04:43:27,309 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/, 内容长度: 33470
2025-07-03 04:43:27,309 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 33,470 字符
2025-07-03 04:43:27,380 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 04:43:27,380 - src.services.web_scraper - INFO -   原始长度: 33,470 字符
2025-07-03 04:43:27,380 - src.services.web_scraper - INFO -   清理后长度: 32,991 字符
2025-07-03 04:43:27,380 - src.services.web_scraper - INFO -   减少比例: 1.4%
2025-07-03 04:43:27,380 - src.services.web_scraper - INFO -   清理统计: 移除0个标签, 0个属性, 0个注释, 3个空标签
2025-07-03 04:43:27,380 - src.core.search_research - INFO - 页面内容适中(32,991字符)，使用单次处理: https://investors.trisaluslifesci.com/
2025-07-03 04:43:27,381 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 04:43:31,390 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 851
2025-07-03 04:43:31,390 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 04:43:31,390 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "xpath_rules": [
    "//div[contains(@class, 'block--news-grid')]//a[contains(@href, '/news-releases/')]",
    "//div[contains(@class, 'nir-widget--list')]//a[@href]",
    "//section[contains(@class, 'nir-widget--content')]//a[@href]"
  ],
  "primary_xpath": "//div[contains(@class, 'block--news-grid')]//a[contains(@href, '/news-releases/')]",
  "confidence": 0.90,
  "structure_analysis": "页面中包含新闻链接的区域主要在类名为'block--news-grid'的div中，链接元素为a标签，通常包含新闻标题和指向新闻详细信息的href属性。新闻日期信息通常在链接前的p标签中，确保提取时考虑到这些结构。",
  "sample_links": [
    "/news-releases/news-release-details/trisalus-life-sciences-announces-commencement-exchange-offer-0",
    "/news-releases/news-release-details/trisalus-life-sciences-announces-launch-trinav-flx-infusion",
    "/news-releases/news-release-details/trisalus-life-sciences-announces-chief-financial-officer"
  ]
}
```
2025-07-03 04:43:31,390 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 04:43:31,391 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/ 提取到 82 个主要XPath，3 个其他规则
2025-07-03 04:43:32,398 - src.core.search_research - INFO - 分析投资者关系页面: https://trisaluslifesci.com/investors/
2025-07-03 04:43:32,398 - src.services.web_scraper - INFO - 开始抓取页面: https://trisaluslifesci.com/investors/
2025-07-03 04:43:32,398 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://trisaluslifesci.com/investors/
2025-07-03 04:43:32,534 - src.services.web_scraper - WARNING - requests页面抓取失败，状态码: 403, URL: https://trisaluslifesci.com/investors/
2025-07-03 04:43:33,548 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://trisaluslifesci.com/investors/
2025-07-03 04:43:33,677 - src.services.web_scraper - WARNING - requests页面抓取失败，状态码: 403, URL: https://trisaluslifesci.com/investors/
2025-07-03 04:43:33,677 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://trisaluslifesci.com/investors/
2025-07-03 04:43:33,677 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://trisaluslifesci.com/investors/
2025-07-03 04:43:41,441 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://trisaluslifesci.com/investors/, 内容长度: 188326
2025-07-03 04:43:41,441 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 188,326 字符
2025-07-03 04:43:41,595 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 04:43:41,595 - src.services.web_scraper - INFO -   原始长度: 188,326 字符
2025-07-03 04:43:41,595 - src.services.web_scraper - INFO -   清理后长度: 33,470 字符
2025-07-03 04:43:41,595 - src.services.web_scraper - INFO -   减少比例: 82.2%
2025-07-03 04:43:41,595 - src.services.web_scraper - INFO -   清理统计: 移除15个标签, 89个属性, 13个注释, 34个空标签
2025-07-03 04:43:41,595 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://trisaluslifesci.com/investors/, 内容长度: 33470
2025-07-03 04:43:41,596 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 33,470 字符
2025-07-03 04:43:41,667 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 04:43:41,668 - src.services.web_scraper - INFO -   原始长度: 33,470 字符
2025-07-03 04:43:41,668 - src.services.web_scraper - INFO -   清理后长度: 32,991 字符
2025-07-03 04:43:41,668 - src.services.web_scraper - INFO -   减少比例: 1.4%
2025-07-03 04:43:41,668 - src.services.web_scraper - INFO -   清理统计: 移除0个标签, 0个属性, 0个注释, 3个空标签
2025-07-03 04:43:41,668 - src.core.search_research - INFO - 页面内容适中(32,991字符)，使用单次处理: https://trisaluslifesci.com/investors/
2025-07-03 04:43:41,668 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 04:43:47,070 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 918
2025-07-03 04:43:47,070 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 04:43:47,070 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "xpath_rules": [
    "//div[contains(@class, 'block--news-grid')]//a[contains(@href, '/news-releases/')]",
    "//div[contains(@class, 'nir-widget--list')]//a[contains(@href, '/news-releases/')]",
    "//div[@class='c-press-feed']//a"
  ],
  "primary_xpath": "//div[contains(@class, 'block--news-grid')]//a[contains(@href, '/news-releases/')]",
  "confidence": 0.90,
  "structure_analysis": "页面中新闻列表位于class为'block--news-grid'的div中，包含多个新闻项，每个新闻项由一个包含链接的a标签组成。每个新闻项还包含日期信息，确保了新闻的时效性和相关性。",
  "sample_links": [
    "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-commencement-exchange-offer-0",
    "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-launch-trinav-flx-infusion",
    "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-chief-financial-officer"
  ]
}
```
2025-07-03 04:43:47,070 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 04:43:47,070 - src.core.search_research - INFO - 从 https://trisaluslifesci.com/investors/ 提取到 82 个主要XPath，3 个其他规则
2025-07-03 04:43:48,077 - src.core.search_research - INFO - XPath规则提取完成:
2025-07-03 04:43:48,077 - src.core.search_research - INFO -   主要XPath: 32 个
2025-07-03 04:43:48,077 - src.core.search_research - INFO -   其他规则: 23 个
2025-07-03 04:43:48,077 - src.core.search_research - INFO -   总计: 55 个唯一规则
2025-07-03 04:43:48,077 - src.core.search_research - INFO - 提取到主要XPath: 32个
2025-07-03 04:43:48,077 - src.core.search_research - INFO - 提取到其他规则: 23个
2025-07-03 04:43:48,077 - src.core.search_research - INFO - 总计XPath规则: 55个
2025-07-03 04:43:48,077 - src.core.search_research - INFO - 公司调研完成: TriSalus
2025-07-03 04:43:49,107 - src.services.drission_scraper - INFO - DrissionPage浏览器实例已关闭
2025-07-03 04:43:49,107 - src.services.web_scraper - INFO - DrissionPage资源已清理
2025-07-03 04:43:49,107 - __main__ - INFO - ✓ TriSalus 调研完成
2025-07-03 04:43:49,108 - __main__ - INFO - 调研结果已保存到: research_results.json
2025-07-03 04:43:49,108 - __main__ - INFO - 所有公司调研完成
2025-07-03 04:47:32,302 - __main__ - INFO - 开始新闻板块发掘功能测试
2025-07-03 04:47:32,302 - __main__ - INFO - 测试时间: 2025-07-03 04:47:32
2025-07-03 04:47:32,302 - __main__ - INFO - ============================================================
2025-07-03 04:47:32,302 - __main__ - INFO - 测试URL分类功能
2025-07-03 04:47:32,303 - __main__ - INFO - ============================================================
2025-07-03 04:47:32,338 - src.services.ai_analyzer - INFO - AI分析服务初始化完成
2025-07-03 04:47:32,338 - __main__ - INFO - 测试 16 个URL的分类
2025-07-03 04:47:32,338 - __main__ - INFO - ✓ https://example.com/investors -> 预期: investor, 实际: investor
2025-07-03 04:47:32,338 - __main__ - INFO - ✓ https://example.com/ir/financials -> 预期: investor, 实际: investor
2025-07-03 04:47:32,339 - __main__ - INFO - ✓ https://example.com/sec-filings -> 预期: investor, 实际: investor
2025-07-03 04:47:32,339 - __main__ - INFO - ✓ https://example.com/shareholder-info -> 预期: investor, 实际: investor
2025-07-03 04:47:32,339 - __main__ - INFO - ✓ https://example.com/quarterly-earnings -> 预期: investor, 实际: investor
2025-07-03 04:47:32,339 - __main__ - INFO - ✓ https://example.com/news -> 预期: news, 实际: news
2025-07-03 04:47:32,339 - __main__ - INFO - ✓ https://example.com/press-releases -> 预期: news, 实际: news
2025-07-03 04:47:32,339 - __main__ - INFO - ✓ https://example.com/media-center -> 预期: news, 实际: news
2025-07-03 04:47:32,339 - __main__ - INFO - ✓ https://example.com/company-blog -> 预期: news, 实际: news
2025-07-03 04:47:32,339 - __main__ - INFO - ✓ https://example.com/announcements -> 预期: news, 实际: news
2025-07-03 04:47:32,339 - __main__ - INFO - ✗ https://example.com/news-and-events -> 预期: mixed, 实际: news
2025-07-03 04:47:32,339 - __main__ - INFO - ✓ https://example.com/investor-news -> 预期: mixed, 实际: mixed
2025-07-03 04:47:32,339 - __main__ - INFO - ✓ https://example.com/press-and-investor -> 预期: mixed, 实际: mixed
2025-07-03 04:47:32,339 - __main__ - INFO - ✓ https://example.com/about -> 预期: unknown, 实际: unknown
2025-07-03 04:47:32,339 - __main__ - INFO - ✓ https://example.com/products -> 预期: unknown, 实际: unknown
2025-07-03 04:47:32,339 - __main__ - INFO - ✓ https://example.com/contact -> 预期: unknown, 实际: unknown
2025-07-03 04:47:32,339 - __main__ - INFO - 分类准确率: 93.8% (15/16)
2025-07-03 04:47:32,339 - __main__ - INFO - ============================================================
2025-07-03 04:47:32,339 - __main__ - INFO - 测试新闻URL验证功能
2025-07-03 04:47:32,340 - __main__ - INFO - ============================================================
2025-07-03 04:47:32,345 - src.services.ai_analyzer - INFO - AI分析服务初始化完成
2025-07-03 04:47:32,345 - __main__ - INFO - ✓ https://example.com/news - 有效新闻URL
2025-07-03 04:47:32,346 - __main__ - INFO - ✓ https://example.com/press-releases - 有效新闻URL
2025-07-03 04:47:32,346 - __main__ - INFO - ✓ https://example.com/media-center - 有效新闻URL
2025-07-03 04:47:32,346 - __main__ - INFO - ✓ https://example.com/company-updates - 有效新闻URL
2025-07-03 04:47:32,346 - __main__ - INFO - ✓ https://example.com/latest-news - 有效新闻URL
2025-07-03 04:47:32,346 - __main__ - INFO - ✓ https://example.com/newsroom - 有效新闻URL
2025-07-03 04:47:32,346 - __main__ - INFO - ✓ https://example.com/blog/company-news - 有效新闻URL
2025-07-03 04:47:32,346 - __main__ - INFO - ✓ https://example.com/about - 正确识别为无效URL
2025-07-03 04:47:32,346 - __main__ - INFO - ✓ https://example.com/products - 正确识别为无效URL
2025-07-03 04:47:32,346 - __main__ - INFO - ✓ https://example.com/contact - 正确识别为无效URL
2025-07-03 04:47:32,346 - __main__ - INFO - ✓ https://example.com/careers - 正确识别为无效URL
2025-07-03 04:47:32,346 - __main__ - INFO - ✓ invalid-url - 正确识别为无效URL
2025-07-03 04:47:32,346 - __main__ - INFO - ✓  - 正确识别为无效URL
2025-07-03 04:47:32,347 - __main__ - INFO - ✓ ftp://example.com/news - 正确识别为无效URL
2025-07-03 04:47:32,347 - __main__ - INFO - 有效新闻URL识别率: 7/7 (100.0%)
2025-07-03 04:47:32,347 - __main__ - INFO - 无效URL过滤率: 7/7 (100.0%)
2025-07-03 04:47:32,347 - __main__ - INFO - ============================================================
2025-07-03 04:47:32,347 - __main__ - INFO - 测试新闻URL优化功能
2025-07-03 04:47:32,347 - __main__ - INFO - ============================================================
2025-07-03 04:47:32,352 - src.services.ai_analyzer - INFO - AI分析服务初始化完成
2025-07-03 04:47:32,352 - __main__ - INFO - 原始URL数量: 11
2025-07-03 04:47:32,352 - __main__ - INFO - 原始URL列表:
2025-07-03 04:47:32,352 - __main__ - INFO -   1. https://example.com/news
2025-07-03 04:47:32,352 - __main__ - INFO -   2. https://example.com/news/
2025-07-03 04:47:32,352 - __main__ - INFO -   3. https://example.com/press-releases
2025-07-03 04:47:32,353 - __main__ - INFO -   4. https://example.com/press-releases
2025-07-03 04:47:32,353 - __main__ - INFO -   5. https://example.com/media-center?tab=news
2025-07-03 04:47:32,353 - __main__ - INFO -   6. https://example.com/media-center?tab=events
2025-07-03 04:47:32,353 - __main__ - INFO -   7. https://example.com/newsroom
2025-07-03 04:47:32,353 - __main__ - INFO -   8. invalid-url
2025-07-03 04:47:32,353 - __main__ - INFO -   9. 
2025-07-03 04:47:32,353 - __main__ - INFO -   10. https://example.com/company-blog
2025-07-03 04:47:32,353 - __main__ - INFO -   11. https://example.com/announcements
2025-07-03 04:47:32,353 - __main__ - INFO - 优化后URL数量: 6
2025-07-03 04:47:32,353 - __main__ - INFO - 优化后URL列表:
2025-07-03 04:47:32,353 - __main__ - INFO -   1. https://example.com/news
2025-07-03 04:47:32,353 - __main__ - INFO -   2. https://example.com/press-releases
2025-07-03 04:47:32,354 - __main__ - INFO -   3. https://example.com/media-center?tab=news
2025-07-03 04:47:32,354 - __main__ - INFO -   4. https://example.com/newsroom
2025-07-03 04:47:32,354 - __main__ - INFO -   5. https://example.com/company-blog
2025-07-03 04:47:32,354 - __main__ - INFO -   6. https://example.com/announcements
2025-07-03 04:47:32,354 - __main__ - INFO - 减少了 5 个URL
2025-07-03 04:47:32,354 - __main__ - INFO - ✓ 成功移除无效URL
2025-07-03 04:47:32,354 - __main__ - INFO - ============================================================
2025-07-03 04:47:32,354 - __main__ - INFO - 测试XPath规则优化功能
2025-07-03 04:47:32,354 - __main__ - INFO - ============================================================
2025-07-03 04:47:32,360 - src.services.ai_analyzer - INFO - AI分析服务初始化完成
2025-07-03 04:47:32,360 - __main__ - INFO - 原始XPath规则数量: 10
2025-07-03 04:47:32,360 - __main__ - INFO - 原始XPath规则:
2025-07-03 04:47:32,360 - __main__ - INFO -   1. //div[@class='news-list']//a[@href]
2025-07-03 04:47:32,361 - __main__ - INFO -   2. //div[@class='news-list']//a
2025-07-03 04:47:32,361 - __main__ - INFO -   3. //div[contains(@class, 'news')]//a[@href]
2025-07-03 04:47:32,361 - __main__ - INFO -   4. //div[contains(@class, 'news')]//a[@href]
2025-07-03 04:47:32,361 - __main__ - INFO -   5. //section[@class='press-releases']//a
2025-07-03 04:47:32,361 - __main__ - INFO -   6. //ul[@class='nav']//a[contains(@href, '/news')]
2025-07-03 04:47:32,361 - __main__ - INFO -   7. 
2025-07-03 04:47:32,361 - __main__ - INFO -   8. //div[@class='news-list']//a[@href]
2025-07-03 04:47:32,361 - __main__ - INFO -   9. //article[@class='news-item']//h2//a[@href]
2025-07-03 04:47:32,361 - __main__ - INFO -   10. //div[@id='newsroom']//li//a
2025-07-03 04:47:32,362 - __main__ - INFO - 优化后XPath规则数量: 4
2025-07-03 04:47:32,362 - __main__ - INFO - 优化后XPath规则:
2025-07-03 04:47:32,362 - __main__ - INFO -   1. //div[contains(@class, 'news')]//a[@href]
2025-07-03 04:47:32,362 - __main__ - INFO -   2. //section[@class='press-releases']//a
2025-07-03 04:47:32,362 - __main__ - INFO -   3. //ul[@class='nav']//a[contains(@href, '/news')]
2025-07-03 04:47:32,362 - __main__ - INFO -   4. //article[@class='news-item']//h2//a[@href]
2025-07-03 04:47:32,362 - __main__ - INFO - 减少了 6 个XPath规则
2025-07-03 04:47:32,362 - __main__ - INFO - ✓ 成功移除空规则
2025-07-03 04:47:32,363 - __main__ - INFO - ============================================================
2025-07-03 04:47:32,363 - __main__ - INFO - 测试完整的新闻板块发掘工作流程
2025-07-03 04:47:32,363 - __main__ - INFO - ============================================================
2025-07-03 04:47:32,363 - __main__ - INFO - 注意: 完整工作流程测试需要:
2025-07-03 04:47:32,363 - __main__ - INFO - 1. 有效的Google Search API密钥
2025-07-03 04:47:32,363 - __main__ - INFO - 2. 有效的OpenAI API密钥
2025-07-03 04:47:32,363 - __main__ - INFO - 3. 网络连接
2025-07-03 04:47:32,363 - __main__ - INFO - 当前仅测试代码结构和逻辑
2025-07-03 04:47:32,363 - src.services.google_search - INFO - Google搜索服务初始化完成
2025-07-03 04:47:32,363 - src.services.web_scraper - INFO - 网页抓取服务初始化完成
2025-07-03 04:47:32,369 - src.services.ai_analyzer - INFO - AI分析服务初始化完成
2025-07-03 04:47:32,369 - src.core.search_research - INFO - SearchResearchClass 初始化完成
2025-07-03 04:47:32,369 - __main__ - INFO - SearchResearchClass 初始化成功
2025-07-03 04:47:32,369 - __main__ - INFO - ✓ _extract_news_sections 方法存在
2025-07-03 04:47:32,369 - __main__ - INFO - ✓ _extract_classified_xpath_rules 方法存在
2025-07-03 04:47:32,369 - __main__ - INFO - ✓ analyze_news_sections 方法存在
2025-07-03 04:47:32,369 - __main__ - INFO - ✓ analyze_classified_news_xpath 方法存在
2025-07-03 04:47:32,369 - __main__ - INFO - 所有新方法都已正确实现
2025-07-03 04:47:32,370 - __main__ - INFO - ============================================================
2025-07-03 04:47:32,370 - __main__ - INFO - 测试结果汇总
2025-07-03 04:47:32,370 - __main__ - INFO - ============================================================
2025-07-03 04:47:32,370 - __main__ - INFO - ✓ 通过 URL分类功能
2025-07-03 04:47:32,370 - __main__ - INFO - ✓ 通过 新闻URL验证功能
2025-07-03 04:47:32,370 - __main__ - INFO - ✓ 通过 新闻URL优化功能
2025-07-03 04:47:32,370 - __main__ - INFO - ✓ 通过 XPath规则优化功能
2025-07-03 04:47:32,370 - __main__ - INFO - ✓ 通过 完整工作流程
2025-07-03 04:47:32,370 - __main__ - INFO - 测试通过率: 100.0% (5/5)
2025-07-03 04:47:32,370 - __main__ - INFO - 新闻板块发掘功能测试整体通过
2025-07-03 04:50:42,177 - __main__ - INFO - AI Tools Find News - 公司调研工具启动
2025-07-03 04:50:42,177 - __main__ - INFO - 准备调研 1 个公司
2025-07-03 04:50:42,177 - __main__ - INFO - 开始调研第 1/1 个公司: TriSalus
2025-07-03 04:50:42,177 - src.services.google_search - INFO - Google搜索服务初始化完成
2025-07-03 04:50:42,177 - src.services.web_scraper - INFO - 网页抓取服务初始化完成
2025-07-03 04:50:42,233 - src.services.ai_analyzer - INFO - AI分析服务初始化完成
2025-07-03 04:50:42,233 - src.core.search_research - INFO - SearchResearchClass 初始化完成
2025-07-03 04:50:42,233 - src.core.search_research - INFO - 开始调研公司: TriSalus
2025-07-03 04:50:42,237 - src.core.search_research - INFO - 步骤1: 搜索公司官网
2025-07-03 04:50:42,237 - src.services.google_search - INFO - 执行Google搜索: TriSalus
2025-07-03 04:50:44,786 - src.services.google_search - INFO - 搜索成功，返回 10 个结果
2025-07-03 04:50:44,788 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 04:50:47,503 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 303
2025-07-03 04:50:47,503 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 04:50:47,503 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "official_website": "https://trisaluslifesci.com/",
  "confidence": 0.98,
  "reasoning": "选择了https://trisaluslifesci.com/作为TriSalus的官方网站，因为该URL包含公司名称，并且是一个主流的.com域名。该页面的标题为'Trisalus Life Sciences'，与公司名称完全一致，且该URL指向的是公司的首页，而不是子页面或第三方平台。其他搜索结果虽然也提到TriSalus，但它们都是子页面或投资者相关的链接，无法作为官方网站首页。"
}
```
2025-07-03 04:50:47,503 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 04:50:47,503 - src.core.search_research - INFO - 找到官网: https://trisaluslifesci.com/
2025-07-03 04:50:47,503 - src.core.search_research - INFO - 步骤2: 分析官网首页，发掘新闻板块
2025-07-03 04:50:47,503 - src.services.web_scraper - INFO - 开始抓取页面: https://trisaluslifesci.com/
2025-07-03 04:50:47,503 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://trisaluslifesci.com/
2025-07-03 04:50:47,625 - src.services.web_scraper - WARNING - requests页面抓取失败，状态码: 403, URL: https://trisaluslifesci.com/
2025-07-03 04:50:48,626 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://trisaluslifesci.com/
2025-07-03 04:50:48,759 - src.services.web_scraper - WARNING - requests页面抓取失败，状态码: 403, URL: https://trisaluslifesci.com/
2025-07-03 04:50:48,759 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://trisaluslifesci.com/
2025-07-03 04:50:49,092 - src.services.drission_scraper - INFO - DrissionPage抓取服务初始化完成
2025-07-03 04:50:49,092 - src.services.web_scraper - INFO - DrissionPage抓取服务已初始化
2025-07-03 04:50:49,092 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://trisaluslifesci.com/
2025-07-03 04:50:49,998 - src.services.drission_scraper - INFO - ChromiumPage实例创建成功
2025-07-03 04:50:58,747 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://trisaluslifesci.com/, 内容长度: 276015
2025-07-03 04:50:58,748 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 276,015 字符
2025-07-03 04:50:59,078 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 04:50:59,078 - src.services.web_scraper - INFO -   原始长度: 276,015 字符
2025-07-03 04:50:59,078 - src.services.web_scraper - INFO -   清理后长度: 131,619 字符
2025-07-03 04:50:59,079 - src.services.web_scraper - INFO -   减少比例: 52.3%
2025-07-03 04:50:59,079 - src.services.web_scraper - INFO -   清理统计: 移除51个标签, 1247个属性, 2个注释, 216个空标签
2025-07-03 04:50:59,079 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://trisaluslifesci.com/, 内容长度: 131619
2025-07-03 04:50:59,079 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 131,619 字符
2025-07-03 04:50:59,273 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 04:50:59,273 - src.services.web_scraper - INFO -   原始长度: 131,619 字符
2025-07-03 04:50:59,273 - src.services.web_scraper - INFO -   清理后长度: 130,599 字符
2025-07-03 04:50:59,273 - src.services.web_scraper - INFO -   减少比例: 0.8%
2025-07-03 04:50:59,273 - src.services.web_scraper - INFO -   清理统计: 移除0个标签, 0个属性, 0个注释, 9个空标签
2025-07-03 04:50:59,273 - src.core.search_research - INFO - HTML内容过长(130,599字符)，使用分批处理
2025-07-03 04:50:59,273 - src.services.web_scraper - INFO - HTML内容过长(130,599字符)，开始智能分割(阈值: 50,000)
2025-07-03 04:50:59,365 - src.services.web_scraper - INFO - 使用选择器 'section' 成功分割为 4 个分片
2025-07-03 04:50:59,366 - src.services.web_scraper - INFO - HTML内容分割完成，共生成 4 个分片
2025-07-03 04:50:59,366 - src.services.ai_analyzer - INFO - 开始分批分析新闻板块，共 4 个分片
2025-07-03 04:50:59,366 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 04:51:06,101 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 1319
2025-07-03 04:51:06,101 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 04:51:06,101 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_relations_urls": [
    "https://investors.trisaluslifesci.com/investor-relations",
    "https://investors.trisaluslifesci.com/news-events/press-releases",
    "https://investors.trisaluslifesci.com/news-events/events-presentations",
    "https://investors.trisaluslifesci.com/governance/governance-overview",
    "https://investors.trisaluslifesci.com/financials/sec-filings",
    "https://investors.trisaluslifesci.com/financials/quarterly-results",
    "https://investors.trisaluslifesci.com/financials/annual-reports",
    "https://investors.trisaluslifesci.com/stock-information",
    "https://investors.trisaluslifesci.com/investor-resources/email-alerts"
  ],
  "news_section_urls": [
    "https://trisaluslifesci.com/news-and-events/",
    "https://trisaluslifesci.com/press-media/"
  ],
  "mixed_section_urls": [],
  "found_keywords": {
    "investor": ["投资者关系", "财务信息", "年报", "季报", "SEC文件", "治理"],
    "news": ["新闻", "Press", "媒体", "公告", "动态"],
    "mixed": []
  },
  "confidence": 0.95,
  "classification_notes": "投资者关系链接主要来自于以'投资者'、'财务'、'治理'等关键词为基础的链接，且这些链接明确指向投资者相关信息。一般新闻链接则是基于'新闻'、'Press'等关键词的链接，且这些链接面向公众。没有找到符合混合板块的链接，因为所有链接都明确归类为投资者关系或一般新闻。",
  "structure_analysis": "页面结构主要由导航菜单组成，包含多个子菜单，分别指向投资者关系和新闻相关的链接。投资者关系链接集中在'Investors'菜单下，而新闻相关链接则在'News & Events'菜单下。整体结构清晰，分类明确。"
}
```
2025-07-03 04:51:06,101 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 04:51:06,102 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 04:51:10,782 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 1219
2025-07-03 04:51:10,782 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 04:51:10,782 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_relations_urls": [
    "https://investors.trisaluslifesci.com/",
    "https://investors.trisaluslifesci.com/news-events/press-releases",
    "https://investors.trisaluslifesci.com/news-events/events-presentations",
    "https://investors.trisaluslifesci.com/governance/governance-overview",
    "https://investors.trisaluslifesci.com/financials/sec-filings",
    "https://investors.trisaluslifesci.com/financials/quarterly-results",
    "https://investors.trisaluslifesci.com/financials/annual-reports",
    "https://investors.trisaluslifesci.com/stock-information",
    "https://investors.trisaluslifesci.com/investor-resources/email-alerts"
  ],
  "news_section_urls": [
    "https://trisaluslifesci.com/news-and-events/",
    "https://trisaluslifesci.com/press-media/"
  ],
  "mixed_section_urls": [],
  "found_keywords": {
    "investor": ["投资者关系", "财务信息", "治理"],
    "news": ["新闻", "Press", "媒体"],
    "mixed": []
  },
  "confidence": 0.95,
  "classification_notes": "在分析过程中，所有链接都经过了关键词匹配和上下文分析。投资者关系链接包含了明确的投资者相关信息，如财务报告和治理结构，而新闻链接则包含了新闻和媒体相关内容。没有找到同时包含投资者和新闻信息的混合板块链接。",
  "structure_analysis": "页面结构主要由主导航菜单构成，包含多个子菜单，分别指向投资者关系和新闻相关的链接。投资者关系链接集中在一个子菜单中，而新闻链接则在另一个子菜单中。整体结构清晰，便于用户导航。"
}
```
2025-07-03 04:51:10,782 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 04:51:10,783 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 04:51:16,666 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 1606
2025-07-03 04:51:16,666 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 04:51:16,666 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_relations_urls": [
    "https://investors.trisaluslifesci.com/",
    "https://investors.trisaluslifesci.com/news-events/press-releases",
    "https://investors.trisaluslifesci.com/governance/governance-overview",
    "https://investors.trisaluslifesci.com/financials/sec-filings",
    "https://investors.trisaluslifesci.com/financials/quarterly-results",
    "https://investors.trisaluslifesci.com/financials/annual-reports",
    "https://investors.trisaluslifesci.com/stock-information",
    "https://investors.trisaluslifesci.com/investor-resources/email-alerts"
  ],
  "news_section_urls": [
    "https://trisaluslifesci.com/news-and-events/",
    "https://trisaluslifesci.com/press-media/",
    "https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-commencement-exchange-offer-0",
    "https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-launch-trinav-flx-infusion",
    "https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-chief-financial-officer"
  ],
  "mixed_section_urls": [],
  "found_keywords": {
    "investor": ["投资者关系", "财务信息", "治理"],
    "news": ["新闻", "Press", "媒体"],
    "mixed": []
  },
  "confidence": 0.95,
  "classification_notes": "在分析过程中，所有链接均根据其内容和上下文进行分类。投资者关系链接包含了与投资者相关的所有信息，如财务报告、治理结构等。一般新闻链接则包含了公司新闻和媒体发布的信息。没有找到符合混合板块的链接，因为所有链接均可明确归类为投资者关系或一般新闻。",
  "structure_analysis": "页面结构主要由导航菜单和内容区域组成。导航菜单清晰地分为多个板块，包括投资者关系、新闻与事件、文化等。投资者关系链接集中在一个子菜单中，而新闻链接则分布在多个地方，包括主导航和内容区域。整体结构逻辑清晰，便于用户查找相关信息。"
}
```
2025-07-03 04:51:16,666 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 04:51:16,667 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 04:51:20,401 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 502
2025-07-03 04:51:20,401 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 04:51:20,402 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_relations_urls": [],
  "news_section_urls": [],
  "mixed_section_urls": [
    "https://trisaluslifesci.com/pipeline/"
  ],
  "found_keywords": {
    "investor": [],
    "news": [],
    "mixed": []
  },
  "confidence": 0.8,
  "classification_notes": "在提供的HTML内容中，没有明确的投资者关系链接和一般新闻链接。唯一找到的链接是指向'PIPELINE'页面，虽然该页面可能包含一些与公司动态相关的信息，但没有明确的新闻或投资者关系内容，因此将其归类为混合板块。",
  "structure_analysis": "页面结构主要由一个包含两个列的section组成，左侧列包含关于公司的管道信息，右侧列包含一个指向'PIPELINE'页面的按钮。没有其他导航元素或链接指向投资者关系或新闻板块。"
}
```
2025-07-03 04:51:20,402 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 04:51:20,402 - src.services.ai_analyzer - INFO - 分批新闻板块分析完成:
2025-07-03 04:51:20,403 - src.services.ai_analyzer - INFO -   投资者关系: 26 -> 10 个唯一URL
2025-07-03 04:51:20,403 - src.services.ai_analyzer - INFO -   新闻板块: 9 -> 5 个唯一URL
2025-07-03 04:51:20,403 - src.services.ai_analyzer - INFO -   混合板块: 1 -> 0 个唯一URL
2025-07-03 04:51:20,403 - src.services.ai_analyzer - INFO -   平均置信度: 0.91
2025-07-03 04:51:20,403 - src.core.search_research - INFO - 分批处理结果: {'total_fragments': 4, 'valid_results': 4, 'total_investor_found': 26, 'total_news_found': 9, 'total_mixed_found': 1, 'unique_investor': 10, 'unique_news': 5, 'unique_mixed': 0}
2025-07-03 04:51:20,403 - src.core.search_research - INFO - 找到投资者关系页面: 10个
2025-07-03 04:51:20,403 - src.core.search_research - INFO - 找到一般新闻页面: 5个
2025-07-03 04:51:20,403 - src.core.search_research - INFO - 找到混合板块页面: 0个
2025-07-03 04:51:20,403 - src.core.search_research - INFO - 步骤3: 分析新闻板块页面，提取分类XPath规则
2025-07-03 04:51:20,403 - src.core.search_research - INFO - 分析investor_relations页面: https://investors.trisaluslifesci.com/investor-relations
2025-07-03 04:51:20,403 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/investor-relations
2025-07-03 04:51:20,403 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/investor-relations
2025-07-03 04:51:25,487 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /investor-relations (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/investor-relations
2025-07-03 04:51:26,496 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/investor-relations
2025-07-03 04:51:31,582 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /investor-relations (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/investor-relations
2025-07-03 04:51:31,582 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/investor-relations
2025-07-03 04:51:31,582 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/investor-relations
2025-07-03 04:51:40,739 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/investor-relations, 内容长度: 188326
2025-07-03 04:51:40,739 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 188,326 字符
2025-07-03 04:51:40,871 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 04:51:40,871 - src.services.web_scraper - INFO -   原始长度: 188,326 字符
2025-07-03 04:51:40,871 - src.services.web_scraper - INFO -   清理后长度: 33,470 字符
2025-07-03 04:51:40,871 - src.services.web_scraper - INFO -   减少比例: 82.2%
2025-07-03 04:51:40,871 - src.services.web_scraper - INFO -   清理统计: 移除15个标签, 89个属性, 13个注释, 34个空标签
2025-07-03 04:51:40,871 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/investor-relations, 内容长度: 33470
2025-07-03 04:51:40,872 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 33,470 字符
2025-07-03 04:51:40,938 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 04:51:40,938 - src.services.web_scraper - INFO -   原始长度: 33,470 字符
2025-07-03 04:51:40,938 - src.services.web_scraper - INFO -   清理后长度: 32,991 字符
2025-07-03 04:51:40,938 - src.services.web_scraper - INFO -   减少比例: 1.4%
2025-07-03 04:51:40,938 - src.services.web_scraper - INFO -   清理统计: 移除0个标签, 0个属性, 0个注释, 3个空标签
2025-07-03 04:51:40,938 - src.core.search_research - INFO - 页面内容适中(32,991字符)，使用单次处理: https://investors.trisaluslifesci.com/investor-relations
2025-07-03 04:51:40,939 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 04:51:47,670 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 1950
2025-07-03 04:51:47,670 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 04:51:47,670 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_xpath_rules": [
    "//div[@class='block--nir-assets__widget']//a[contains(@href, '/financials/')]",
    "//div[@class='block--nir-assets__widget']//a[contains(@href, '/sec-filings/')]",
    "//div[@class='block--nir-assets__widget']//a[contains(@href, '/news-releases/')]"
  ],
  "news_xpath_rules": [
    "//div[@class='c-press-feed']//a[@href]",
    "//div[@class='nir-widget--news--headline']//a[@href]",
    "//section[contains(@class, 'block--news-grid')]//a[@href]"
  ],
  "general_xpath_rules": [
    "//div[@class='c-quicklinks']//a[@href]",
    "//section[contains(@class, 'c-calltoaction')]//a[@href]"
  ],
  "primary_xpath": {
    "investor": "//div[@class='block--nir-assets__widget']//a[contains(@href, '/financials/')]",
    "news": "//div[@class='c-press-feed']//a[@href]",
    "general": "//div[@class='c-quicklinks']//a[@href]"
  },
  "confidence": 0.90,
  "page_structure": "页面包含多个主要部分，包括导航栏、投资者关系信息、新闻发布、财务报告和公司概况。主要内容区域通过不同的class和section标签进行区分，投资者相关内容集中在特定的widget中，而新闻内容则在另一个widget中显示。",
  "content_classification": "内容分类依据主要是通过HTML结构中的class和section标签来区分投资者信息和新闻信息。投资者信息通常包含财务报告和SEC文件的链接，而新闻信息则包含公司新闻和产品发布的链接。",
  "sample_links": {
    "investor": [
      "https://trisaluslifesci.com/financials/sec-filings",
      "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-commencement-exchange-offer-0",
      "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-launch-trinav-flx-infusion"
    ],
    "news": [
      "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-chief-financial-officer",
      "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-commencement-exchange-offer-0",
      "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-launch-trinav-flx-infusion"
    ]
  }
}
```
2025-07-03 04:51:47,670 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 04:51:47,671 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/investor-relations 提取到: 投资者3个, 新闻3个, 通用2个XPath规则
2025-07-03 04:51:48,674 - src.core.search_research - INFO - 分析investor_relations页面: https://investors.trisaluslifesci.com/news-events/press-releases
2025-07-03 04:51:48,674 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/news-events/press-releases
2025-07-03 04:51:48,674 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/news-events/press-releases
2025-07-03 04:51:53,766 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /news-events/press-releases (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/news-events/press-releases
2025-07-03 04:51:54,769 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/news-events/press-releases
2025-07-03 04:51:59,856 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /news-events/press-releases (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/news-events/press-releases
2025-07-03 04:51:59,856 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/news-events/press-releases
2025-07-03 04:51:59,856 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/news-events/press-releases
2025-07-03 04:52:08,380 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/news-events/press-releases, 内容长度: 193750
2025-07-03 04:52:08,380 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 193,750 字符
2025-07-03 04:52:08,543 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 04:52:08,543 - src.services.web_scraper - INFO -   原始长度: 193,750 字符
2025-07-03 04:52:08,543 - src.services.web_scraper - INFO -   清理后长度: 35,090 字符
2025-07-03 04:52:08,543 - src.services.web_scraper - INFO -   减少比例: 81.9%
2025-07-03 04:52:08,543 - src.services.web_scraper - INFO -   清理统计: 移除8个标签, 112个属性, 6个注释, 27个空标签
2025-07-03 04:52:08,543 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/news-events/press-releases, 内容长度: 35090
2025-07-03 04:52:08,543 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 35,090 字符
2025-07-03 04:52:08,616 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 04:52:08,616 - src.services.web_scraper - INFO -   原始长度: 35,090 字符
2025-07-03 04:52:08,616 - src.services.web_scraper - INFO -   清理后长度: 34,611 字符
2025-07-03 04:52:08,616 - src.services.web_scraper - INFO -   减少比例: 1.4%
2025-07-03 04:52:08,617 - src.services.web_scraper - INFO -   清理统计: 移除0个标签, 0个属性, 0个注释, 3个空标签
2025-07-03 04:52:08,617 - src.core.search_research - INFO - 页面内容适中(34,611字符)，使用单次处理: https://investors.trisaluslifesci.com/news-events/press-releases
2025-07-03 04:52:08,617 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 04:52:13,630 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 1581
2025-07-03 04:52:13,630 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 04:52:13,630 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_xpath_rules": [],
  "news_xpath_rules": [
    "//article[contains(@class, 'node--nir-news--nir-widget-list')]//div[contains(@class, 'nir-widget--news--headline')]//a[@href]",
    "//div[@class='nir-widget--list']//article//a[@href]",
    "//div[@class='nir-widget--content']//a[contains(@href, '/news-releases/')]"
  ],
  "general_xpath_rules": [],
  "primary_xpath": {
    "investor": "",
    "news": "//article[contains(@class, 'node--nir-news--nir-widget-list')]//div[contains(@class, 'nir-widget--news--headline')]//a[@href]",
    "general": ""
  },
  "confidence": 0.9,
  "page_structure": "该页面主要由一个包含新闻发布的区域构成，使用了多个article元素来展示每条新闻。每个新闻条目包含标题、发布日期和摘要信息，标题链接指向详细的新闻页面。页面还包含导航栏和页脚，提供了其他相关链接。",
  "content_classification": "页面主要集中在新闻发布内容上，包含公司新闻、产品发布等信息，未包含投资者关系相关的内容。",
  "sample_links": {
    "investor": [],
    "news": [
      "https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-commencement-exchange-offer-0",
      "https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-launch-trinav-flx-infusion",
      "https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-chief-financial-officer",
      "https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-reports-first-quarter-2025-results-and",
      "https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-host-first-quarter-2025-financial-results"
    ]
  }
}
```
2025-07-03 04:52:13,630 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 04:52:13,630 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/news-events/press-releases 提取到: 投资者0个, 新闻3个, 通用0个XPath规则
2025-07-03 04:52:14,643 - src.core.search_research - INFO - 分析investor_relations页面: https://investors.trisaluslifesci.com/news-events/events-presentations
2025-07-03 04:52:14,643 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/news-events/events-presentations
2025-07-03 04:52:14,643 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/news-events/events-presentations
2025-07-03 04:52:19,724 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /news-events/events-presentations (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/news-events/events-presentations
2025-07-03 04:52:20,734 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/news-events/events-presentations
2025-07-03 04:52:25,820 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /news-events/events-presentations (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/news-events/events-presentations
2025-07-03 04:52:25,820 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/news-events/events-presentations
2025-07-03 04:52:25,820 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/news-events/events-presentations
2025-07-03 04:52:34,511 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/news-events/events-presentations, 内容长度: 180921
2025-07-03 04:52:34,512 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 180,921 字符
2025-07-03 04:52:34,648 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 04:52:34,648 - src.services.web_scraper - INFO -   原始长度: 180,921 字符
2025-07-03 04:52:34,648 - src.services.web_scraper - INFO -   清理后长度: 29,940 字符
2025-07-03 04:52:34,648 - src.services.web_scraper - INFO -   减少比例: 83.5%
2025-07-03 04:52:34,648 - src.services.web_scraper - INFO -   清理统计: 移除8个标签, 83个属性, 6个注释, 17个空标签
2025-07-03 04:52:34,648 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/news-events/events-presentations, 内容长度: 29940
2025-07-03 04:52:34,648 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 29,940 字符
2025-07-03 04:52:34,720 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 04:52:34,720 - src.services.web_scraper - INFO -   原始长度: 29,940 字符
2025-07-03 04:52:34,720 - src.services.web_scraper - INFO -   清理后长度: 29,461 字符
2025-07-03 04:52:34,720 - src.services.web_scraper - INFO -   减少比例: 1.6%
2025-07-03 04:52:34,720 - src.services.web_scraper - INFO -   清理统计: 移除0个标签, 0个属性, 0个注释, 3个空标签
2025-07-03 04:52:34,720 - src.core.search_research - INFO - 页面内容适中(29,461字符)，使用单次处理: https://investors.trisaluslifesci.com/news-events/events-presentations
2025-07-03 04:52:34,721 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 04:52:42,475 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 1928
2025-07-03 04:52:42,475 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 04:52:42,476 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_xpath_rules": [
    "//div[@class='nir-widget--field nir-widget--asset--list-of-files']//a[@href]",
    "//div[@class='nir-widget--field nir-widget--event--title']//a[@href]",
    "//div[@class='nir-widget--field nir-widget--event--webcast']//a[@href]"
  ],
  "news_xpath_rules": [
    "//article[contains(@class, 'node--nir-event')]//div[contains(@class, 'field-nir-event-title')]//a[@href]",
    "//article[contains(@class, 'node--nir-asset')]//div[contains(@class, 'field--name-field-nir-document')]//a[@href]"
  ],
  "general_xpath_rules": [
    "//div[@class='nir-widget--list']//a[@href]",
    "//section[@class='top-section']//h1/following-sibling::div//a[@href]"
  ],
  "primary_xpath": {
    "investor": "//div[@class='nir-widget--field nir-widget--asset--list-of-files']//a[@href]",
    "news": "//article[contains(@class, 'node--nir-event')]//div[contains(@class, 'field-nir-event-title')]//a[@href]",
    "general": "//div[@class='nir-widget--list']//a[@href]"
  },
  "confidence": 0.85,
  "page_structure": "页面主要由多个section组成，包含导航栏、事件和演示文稿的列表。每个事件和演示文稿都在article标签内，包含日期、标题和链接。内容通过不同的class进行区分，投资者相关内容和新闻内容分别在不同的widget中展示。",
  "content_classification": "内容分类依据是根据HTML结构中的class和标签类型进行区分，投资者关系内容主要集中在包含财务报告和事件的widget中，而新闻内容则在包含公司新闻和事件的article标签中。",
  "sample_links": {
    "investor": [
      "https://edge.media-server.com/mmc/p/vqrjxogk",
      "https://edge.media-server.com/mmc/p/g6oxnay5",
      "https://edge.media-server.com/mmc/p/w7fp8yvn",
      "https://edge.media-server.com/mmc/p/2hmvqta7/",
      "https://edge.media-server.com/mmc/p/hvbgap5o"
    ],
    "news": [
      "https://trisaluslifesci.com/news-and-events/#events",
      "https://trisaluslifesci.com/news-and-events/#events",
      "https://trisaluslifesci.com/news-and-events/#events",
      "https://trisaluslifesci.com/news-and-events/#events",
      "https://trisaluslifesci.com/news-and-events/#events"
    ]
  }
}
```
2025-07-03 04:52:42,476 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 04:52:42,476 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/news-events/events-presentations 提取到: 投资者3个, 新闻2个, 通用2个XPath规则
2025-07-03 04:52:43,479 - src.core.search_research - INFO - 分析investor_relations页面: https://investors.trisaluslifesci.com/governance/governance-overview
2025-07-03 04:52:43,479 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/governance/governance-overview
2025-07-03 04:52:43,479 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/governance/governance-overview
2025-07-03 04:52:48,559 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /governance/governance-overview (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/governance/governance-overview
2025-07-03 04:52:49,566 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/governance/governance-overview
2025-07-03 04:52:54,652 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /governance/governance-overview (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/governance/governance-overview
2025-07-03 04:52:54,652 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/governance/governance-overview
2025-07-03 04:52:54,652 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/governance/governance-overview
2025-07-03 04:53:02,843 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/governance/governance-overview, 内容长度: 168988
2025-07-03 04:53:02,843 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 168,988 字符
2025-07-03 04:53:02,938 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 04:53:02,938 - src.services.web_scraper - INFO -   原始长度: 168,988 字符
2025-07-03 04:53:02,938 - src.services.web_scraper - INFO -   清理后长度: 22,278 字符
2025-07-03 04:53:02,938 - src.services.web_scraper - INFO -   减少比例: 86.8%
2025-07-03 04:53:02,938 - src.services.web_scraper - INFO -   清理统计: 移除8个标签, 74个属性, 6个注释, 23个空标签
2025-07-03 04:53:02,938 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/governance/governance-overview, 内容长度: 22278
2025-07-03 04:53:02,938 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 22,278 字符
2025-07-03 04:53:02,988 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 04:53:02,988 - src.services.web_scraper - INFO -   原始长度: 22,278 字符
2025-07-03 04:53:02,988 - src.services.web_scraper - INFO -   清理后长度: 21,799 字符
2025-07-03 04:53:02,988 - src.services.web_scraper - INFO -   减少比例: 2.2%
2025-07-03 04:53:02,988 - src.services.web_scraper - INFO -   清理统计: 移除0个标签, 0个属性, 0个注释, 3个空标签
2025-07-03 04:53:02,988 - src.core.search_research - INFO - 页面内容适中(21,799字符)，使用单次处理: https://investors.trisaluslifesci.com/governance/governance-overview
2025-07-03 04:53:02,989 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 04:53:08,243 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 1574
2025-07-03 04:53:08,243 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 04:53:08,243 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_xpath_rules": [
    "//div[@class='nir-widget--list']//a[contains(@href, '/static-files')]",
    "//div[@class='nir-widget--list']//a[contains(@href, '/financials')]",
    "//div[@class='nir-widget--list']//a[contains(@href, '/governance')]"
  ],
  "news_xpath_rules": [
    "//ul[@class='navbar-secondary-links']//a[contains(@href, '/news-and-events')]",
    "//div[@class='nir-widget--list']//a[contains(@href, '/press-media')]",
    "//div[@class='nir-widget--list']//a[contains(@href, '/news-events')]"
  ],
  "general_xpath_rules": [
    "//div[@class='region region-content']//a[@href]",
    "//section[@class='top-section']//a[@href]"
  ],
  "primary_xpath": {
    "investor": "//div[@class='nir-widget--list']//a[contains(@href, '/static-files')]",
    "news": "//ul[@class='navbar-secondary-links']//a[contains(@href, '/news-and-events')]",
    "general": "//div[@class='region region-content']//a[@href]"
  },
  "confidence": 0.85,
  "page_structure": "该页面主要由导航栏、内容区域和页脚组成。导航栏包含多个下拉菜单，内容区域包含治理概述和相关文档的链接，页脚包含版权信息和其他链接。",
  "content_classification": "内容分类依据主要是链接的URL结构和所在的HTML元素。投资者关系内容主要集中在治理文档和财务报告的链接，而新闻内容则集中在新闻和事件的链接。",
  "sample_links": {
    "investor": [
      "https://trisaluslifesci.com/static-files/43e281ed-94d0-435e-90b7-ec78a928def7",
      "https://trisaluslifesci.com/static-files/62569002-5a5a-4aac-a4d4-59932b437b9a",
      "https://trisaluslifesci.com/governance/governance-overview"
    ],
    "news": [
      "https://trisaluslifesci.com/news-and-events/",
      "https://trisaluslifesci.com/press-media/"
    ]
  }
}
```
2025-07-03 04:53:08,243 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 04:53:08,243 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/governance/governance-overview 提取到: 投资者3个, 新闻3个, 通用2个XPath规则
2025-07-03 04:53:09,246 - src.core.search_research - INFO - 分析investor_relations页面: https://investors.trisaluslifesci.com/financials/sec-filings
2025-07-03 04:53:09,246 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/financials/sec-filings
2025-07-03 04:53:09,246 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/financials/sec-filings
2025-07-03 04:53:14,319 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /financials/sec-filings (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/financials/sec-filings
2025-07-03 04:53:15,324 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/financials/sec-filings
2025-07-03 04:53:20,416 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /financials/sec-filings (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/financials/sec-filings
2025-07-03 04:53:20,416 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/financials/sec-filings
2025-07-03 04:53:20,416 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/financials/sec-filings
2025-07-03 04:53:28,069 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/financials/sec-filings, 内容长度: 201005
2025-07-03 04:53:28,069 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 201,005 字符
2025-07-03 04:53:28,233 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 04:53:28,233 - src.services.web_scraper - INFO -   原始长度: 201,005 字符
2025-07-03 04:53:28,233 - src.services.web_scraper - INFO -   清理后长度: 36,893 字符
2025-07-03 04:53:28,233 - src.services.web_scraper - INFO -   减少比例: 81.6%
2025-07-03 04:53:28,233 - src.services.web_scraper - INFO -   清理统计: 移除8个标签, 139个属性, 6个注释, 52个空标签
2025-07-03 04:53:28,233 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/financials/sec-filings, 内容长度: 36893
2025-07-03 04:53:28,233 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 36,893 字符
2025-07-03 04:53:28,325 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 04:53:28,325 - src.services.web_scraper - INFO -   原始长度: 36,893 字符
2025-07-03 04:53:28,325 - src.services.web_scraper - INFO -   清理后长度: 36,414 字符
2025-07-03 04:53:28,325 - src.services.web_scraper - INFO -   减少比例: 1.3%
2025-07-03 04:53:28,325 - src.services.web_scraper - INFO -   清理统计: 移除0个标签, 0个属性, 0个注释, 3个空标签
2025-07-03 04:53:28,326 - src.core.search_research - INFO - 页面内容适中(36,414字符)，使用单次处理: https://investors.trisaluslifesci.com/financials/sec-filings
2025-07-03 04:53:28,326 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 04:53:33,467 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 1399
2025-07-03 04:53:33,467 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 04:53:33,467 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_xpath_rules": [
    "//table[@class='nirtable views-table views-view-table cols-4 collapse-table-wide']//td/a[contains(@href, '/sec-filings/sec-filing')]",
    "//div[@class='view-content']//a[contains(@href, '/financials/')]",
    "//div[@class='view-content']//a[contains(@href, '/financials/sec-filings')]"
  ],
  "news_xpath_rules": [],
  "general_xpath_rules": [],
  "primary_xpath": {
    "investor": "//table[@class='nirtable views-table views-view-table cols-4 collapse-table-wide']//td/a[contains(@href, '/sec-filings/sec-filing')]",
    "news": "",
    "general": ""
  },
  "confidence": 0.9,
  "page_structure": "页面主要由一个表格组成，包含SEC文件的相关信息。表格的每一行代表一个文件，包含日期、文件类型、描述和查看链接。表格外部有过滤器和分页导航。",
  "content_classification": "页面主要集中在投资者关系内容，特别是SEC文件和财务报告，未包含一般新闻内容。",
  "sample_links": {
    "investor": [
      "https://investors.trisaluslifesci.com/financials/sec-filings/sec-filing/4/0001826667-25-000079",
      "https://investors.trisaluslifesci.com/financials/sec-filings/sec-filing/4/0001826667-25-000083",
      "https://investors.trisaluslifesci.com/financials/sec-filings/sec-filing/4/0001826667-25-000081",
      "https://investors.trisaluslifesci.com/financials/sec-filings/sec-filing/4/0001826667-25-000087",
      "https://investors.trisaluslifesci.com/financials/sec-filings/sec-filing/8-k/0001826667-25-000077"
    ],
    "news": [],
    "general": []
  }
}
```
2025-07-03 04:53:33,468 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 04:53:33,468 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/financials/sec-filings 提取到: 投资者3个, 新闻0个, 通用0个XPath规则
2025-07-03 04:53:34,473 - src.core.search_research - INFO - 分析investor_relations页面: https://investors.trisaluslifesci.com/financials/quarterly-results
2025-07-03 04:53:34,473 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/financials/quarterly-results
2025-07-03 04:53:34,473 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/financials/quarterly-results
2025-07-03 04:53:39,562 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /financials/quarterly-results (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/financials/quarterly-results
2025-07-03 04:53:40,565 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/financials/quarterly-results
2025-07-03 04:53:45,671 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /financials/quarterly-results (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/financials/quarterly-results
2025-07-03 04:53:45,671 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/financials/quarterly-results
2025-07-03 04:53:45,671 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/financials/quarterly-results
2025-07-03 04:53:53,637 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/financials/quarterly-results, 内容长度: 170413
2025-07-03 04:53:53,638 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 170,413 字符
2025-07-03 04:53:53,748 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 04:53:53,748 - src.services.web_scraper - INFO -   原始长度: 170,413 字符
2025-07-03 04:53:53,748 - src.services.web_scraper - INFO -   清理后长度: 22,137 字符
2025-07-03 04:53:53,748 - src.services.web_scraper - INFO -   减少比例: 87.0%
2025-07-03 04:53:53,748 - src.services.web_scraper - INFO -   清理统计: 移除8个标签, 77个属性, 6个注释, 19个空标签
2025-07-03 04:53:53,748 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/financials/quarterly-results, 内容长度: 22137
2025-07-03 04:53:53,748 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 22,137 字符
2025-07-03 04:53:53,805 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 04:53:53,805 - src.services.web_scraper - INFO -   原始长度: 22,137 字符
2025-07-03 04:53:53,805 - src.services.web_scraper - INFO -   清理后长度: 21,658 字符
2025-07-03 04:53:53,805 - src.services.web_scraper - INFO -   减少比例: 2.2%
2025-07-03 04:53:53,805 - src.services.web_scraper - INFO -   清理统计: 移除0个标签, 0个属性, 0个注释, 3个空标签
2025-07-03 04:53:53,806 - src.core.search_research - INFO - 页面内容适中(21,658字符)，使用单次处理: https://investors.trisaluslifesci.com/financials/quarterly-results
2025-07-03 04:53:53,806 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 04:53:58,256 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 1192
2025-07-03 04:53:58,257 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 04:53:58,257 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_xpath_rules": [
    "//div[@class='views-field views-field-field-nir-bundle-content']//a[@href]",
    "//div[contains(@class, 'qrGridContent')]//a[@href]",
    "//div[@class='item-list']//a[@href]"
  ],
  "news_xpath_rules": [],
  "general_xpath_rules": [],
  "primary_xpath": {
    "investor": "//div[@class='views-field views-field-field-nir-bundle-content']//a[@href]",
    "news": "",
    "general": ""
  },
  "confidence": 0.9,
  "page_structure": "该页面主要由导航栏、财务结果部分和联系信息部分组成。财务结果部分包含多个季度的财务报告链接，使用了div和ul/li结构来组织内容。每个季度的财务报告都在一个独立的div中，包含多个链接，指向不同的财务文件。",
  "content_classification": "页面主要集中在投资者关系内容，特别是季度财务报告和SEC文件。没有明显的新闻内容部分，因此没有找到相关的新闻XPath规则。",
  "sample_links": {
    "investor": [
      "/news-releases/news-release-details/trisalus-life-sciences-reports-q4-and-full-year-2024-financial",
      "/sec-filings/sec-filing/nt-10-k/0001826667-25-000027",
      "/news-releases/news-release-details/trisalus-reports-q3-2024-financial-results-and-provides-business",
      "/sec-filings/sec-filing/10-q/0001826667-24-000051",
      "/news-releases/news-release-details/trisalus-reports-q2-2024-financial-results-and-business-update"
    ],
    "news": []
  }
}
```
2025-07-03 04:53:58,257 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 04:53:58,257 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/financials/quarterly-results 提取到: 投资者3个, 新闻0个, 通用0个XPath规则
2025-07-03 04:53:59,268 - src.core.search_research - INFO - 分析investor_relations页面: https://investors.trisaluslifesci.com/financials/annual-reports
2025-07-03 04:53:59,268 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/financials/annual-reports
2025-07-03 04:53:59,268 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/financials/annual-reports
2025-07-03 04:54:04,349 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /financials/annual-reports (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/financials/annual-reports
2025-07-03 04:54:05,356 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/financials/annual-reports
2025-07-03 04:54:10,427 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /financials/annual-reports (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/financials/annual-reports
2025-07-03 04:54:10,427 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/financials/annual-reports
2025-07-03 04:54:10,427 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/financials/annual-reports
2025-07-03 04:54:17,905 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/financials/annual-reports, 内容长度: 160080
2025-07-03 04:54:17,905 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 160,080 字符
2025-07-03 04:54:17,978 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 04:54:17,978 - src.services.web_scraper - INFO -   原始长度: 160,080 字符
2025-07-03 04:54:17,978 - src.services.web_scraper - INFO -   清理后长度: 15,826 字符
2025-07-03 04:54:17,978 - src.services.web_scraper - INFO -   减少比例: 90.1%
2025-07-03 04:54:17,978 - src.services.web_scraper - INFO -   清理统计: 移除8个标签, 68个属性, 6个注释, 16个空标签
2025-07-03 04:54:17,978 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/financials/annual-reports, 内容长度: 15826
2025-07-03 04:54:17,978 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 15,826 字符
2025-07-03 04:54:18,017 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 04:54:18,017 - src.services.web_scraper - INFO -   原始长度: 15,826 字符
2025-07-03 04:54:18,017 - src.services.web_scraper - INFO -   清理后长度: 15,347 字符
2025-07-03 04:54:18,017 - src.services.web_scraper - INFO -   减少比例: 3.0%
2025-07-03 04:54:18,017 - src.services.web_scraper - INFO -   清理统计: 移除0个标签, 0个属性, 0个注释, 3个空标签
2025-07-03 04:54:18,017 - src.core.search_research - INFO - 页面内容适中(15,347字符)，使用单次处理: https://investors.trisaluslifesci.com/financials/annual-reports
2025-07-03 04:54:18,018 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 04:54:22,205 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 1091
2025-07-03 04:54:22,205 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 04:54:22,205 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_xpath_rules": [
    "//div[@class='block--nir-assets__widget']//a[contains(@href, '/financials/annual-reports')]",
    "//div[@class='block--nir-assets__widget']//a[contains(@href, '/financials/sec-filings')]",
    "//div[@class='block--nir-assets__widget']//a[contains(@href, '/financials/quarterly-results')]"
  ],
  "news_xpath_rules": [],
  "general_xpath_rules": [],
  "primary_xpath": {
    "investor": "//div[@class='block--nir-assets__widget']//a[contains(@href, '/financials/annual-reports')]",
    "news": "",
    "general": ""
  },
  "confidence": 0.9,
  "page_structure": "页面主要由导航栏、标题、内容区域和页脚组成。内容区域包含一个主要的投资者关系部分，展示了与财务报告相关的链接。没有明显的新闻内容部分，页面结构较为简单，主要集中在投资者信息上。",
  "content_classification": "页面主要内容为投资者关系信息，包含财务报告、SEC文件等链接，没有包含一般新闻内容，因此分类为投资者关系页面。",
  "sample_links": {
    "investor": [
      "https://investors.trisaluslifesci.com/financials/annual-reports",
      "https://investors.trisaluslifesci.com/financials/sec-filings",
      "https://investors.trisaluslifesci.com/financials/quarterly-results"
    ],
    "news": [],
    "general": []
  }
}
```
2025-07-03 04:54:22,206 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 04:54:22,206 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/financials/annual-reports 提取到: 投资者3个, 新闻0个, 通用0个XPath规则
2025-07-03 04:54:23,208 - src.core.search_research - INFO - 分析investor_relations页面: https://investors.trisaluslifesci.com/stock-information
2025-07-03 04:54:23,208 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/stock-information
2025-07-03 04:54:23,208 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/stock-information
2025-07-03 04:54:28,292 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /stock-information (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/stock-information
2025-07-03 04:54:29,299 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/stock-information
2025-07-03 04:54:34,392 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /stock-information (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/stock-information
2025-07-03 04:54:34,392 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/stock-information
2025-07-03 04:54:34,392 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/stock-information
2025-07-03 04:54:46,179 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/stock-information, 内容长度: 193189
2025-07-03 04:54:46,179 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 193,189 字符
2025-07-03 04:54:46,347 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 04:54:46,347 - src.services.web_scraper - INFO -   原始长度: 193,189 字符
2025-07-03 04:54:46,347 - src.services.web_scraper - INFO -   清理后长度: 33,124 字符
2025-07-03 04:54:46,347 - src.services.web_scraper - INFO -   减少比例: 82.9%
2025-07-03 04:54:46,348 - src.services.web_scraper - INFO -   清理统计: 移除10个标签, 148个属性, 6个注释, 48个空标签
2025-07-03 04:54:46,348 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/stock-information, 内容长度: 33124
2025-07-03 04:54:46,348 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 33,124 字符
2025-07-03 04:54:46,459 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 04:54:46,459 - src.services.web_scraper - INFO -   原始长度: 33,124 字符
2025-07-03 04:54:46,459 - src.services.web_scraper - INFO -   清理后长度: 32,645 字符
2025-07-03 04:54:46,459 - src.services.web_scraper - INFO -   减少比例: 1.4%
2025-07-03 04:54:46,459 - src.services.web_scraper - INFO -   清理统计: 移除0个标签, 0个属性, 0个注释, 3个空标签
2025-07-03 04:54:46,459 - src.core.search_research - INFO - 页面内容适中(32,645字符)，使用单次处理: https://investors.trisaluslifesci.com/stock-information
2025-07-03 04:54:46,460 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 04:54:54,593 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 1643
2025-07-03 04:54:54,593 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 04:54:54,593 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_xpath_rules": [
    "//ul[contains(@class, 'navbar-secondary-links')]//a[contains(@href, '/financials')]",
    "//ul[contains(@class, 'navbar-secondary-links')]//a[contains(@href, '/investor-resources')]",
    "//ul[contains(@class, 'navbar-secondary-links')]//a[contains(@href, '/governance')]"
  ],
  "news_xpath_rules": [
    "//ul[contains(@class, 'navbar-secondary-links')]//a[contains(@href, '/news-and-events')]",
    "//ul[contains(@class, 'navbar-secondary-links')]//a[contains(@href, '/press-media')]",
    "//article[contains(@class, 'node--type-nir_landing_page')]//a[contains(@href, '/news-events')]"
  ],
  "general_xpath_rules": [
    "//div[@class='c-calltoaction']//a[@href]",
    "//section[contains(@class, 'top-section')]//a[@href]"
  ],
  "primary_xpath": {
    "investor": "//ul[contains(@class, 'navbar-secondary-links')]//a[contains(@href, '/financials')]",
    "news": "//ul[contains(@class, 'navbar-secondary-links')]//a[contains(@href, '/news-and-events')]",
    "general": "//div[@class='c-calltoaction']//a[@href]"
  },
  "confidence": 0.85,
  "page_structure": "页面包含多个导航栏和内容区域，主要分为投资者关系和新闻部分。投资者关系链接集中在二级导航栏中，新闻链接则在同一导航栏的不同子菜单中。内容区域包括多个section和article，展示了股价信息、历史数据和投资者联系信息。",
  "content_classification": "内容分类依据主要是链接的href属性和所在的HTML结构。投资者关系内容主要集中在财务报告和治理文档相关链接，而新闻内容则集中在公司新闻和媒体报道相关链接。",
  "sample_links": {
    "investor": [
      "https://trisaluslifesci.com/financials/sec-filings",
      "https://trisaluslifesci.com/investor-resources/email-alerts"
    ],
    "news": [
      "https://trisaluslifesci.com/news-and-events/",
      "https://trisaluslifesci.com/press-media/"
    ]
  }
}
```
2025-07-03 04:54:54,593 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 04:54:54,593 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/stock-information 提取到: 投资者3个, 新闻3个, 通用2个XPath规则
2025-07-03 04:54:55,606 - src.core.search_research - INFO - 分析investor_relations页面: https://investors.trisaluslifesci.com/investor-resources/email-alerts
2025-07-03 04:54:55,606 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/investor-resources/email-alerts
2025-07-03 04:54:55,606 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/investor-resources/email-alerts
2025-07-03 04:55:00,677 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /investor-resources/email-alerts (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/investor-resources/email-alerts
2025-07-03 04:55:01,685 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/investor-resources/email-alerts
2025-07-03 04:55:06,772 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /investor-resources/email-alerts (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/investor-resources/email-alerts
2025-07-03 04:55:06,772 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/investor-resources/email-alerts
2025-07-03 04:55:06,772 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/investor-resources/email-alerts
2025-07-03 04:55:15,561 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/investor-resources/email-alerts, 内容长度: 171319
2025-07-03 04:55:15,562 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 171,319 字符
2025-07-03 04:55:15,649 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 04:55:15,649 - src.services.web_scraper - INFO -   原始长度: 171,319 字符
2025-07-03 04:55:15,649 - src.services.web_scraper - INFO -   清理后长度: 20,872 字符
2025-07-03 04:55:15,649 - src.services.web_scraper - INFO -   减少比例: 87.8%
2025-07-03 04:55:15,650 - src.services.web_scraper - INFO -   清理统计: 移除11个标签, 103个属性, 6个注释, 20个空标签
2025-07-03 04:55:15,650 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/investor-resources/email-alerts, 内容长度: 20872
2025-07-03 04:55:15,650 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 20,872 字符
2025-07-03 04:55:15,696 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 04:55:15,696 - src.services.web_scraper - INFO -   原始长度: 20,872 字符
2025-07-03 04:55:15,696 - src.services.web_scraper - INFO -   清理后长度: 20,393 字符
2025-07-03 04:55:15,696 - src.services.web_scraper - INFO -   减少比例: 2.3%
2025-07-03 04:55:15,696 - src.services.web_scraper - INFO -   清理统计: 移除0个标签, 0个属性, 0个注释, 3个空标签
2025-07-03 04:55:15,696 - src.core.search_research - INFO - 页面内容适中(20,393字符)，使用单次处理: https://investors.trisaluslifesci.com/investor-resources/email-alerts
2025-07-03 04:55:15,697 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 04:55:20,224 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 1346
2025-07-03 04:55:20,224 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 04:55:20,224 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_xpath_rules": [
    "//a[contains(@href, '/financials/')]", 
    "//a[contains(@href, '/investor-resources/')]", 
    "//a[contains(@href, '/governance/')]", 
    "//a[contains(@href, '/news-events/press-releases')]"
  ],
  "news_xpath_rules": [
    "//a[contains(@href, '/news-and-events/')]", 
    "//a[contains(@href, '/press-media/')]", 
    "//a[contains(@href, '/news-events/events-presentations')]"
  ],
  "general_xpath_rules": [],
  "primary_xpath": {
    "investor": "//a[contains(@href, '/financials/')]",
    "news": "//a[contains(@href, '/news-and-events/')]",
    "general": ""
  },
  "confidence": 0.9,
  "page_structure": "页面主要由导航栏、内容区域和页脚组成。导航栏包含多个下拉菜单，内容区域包含一个主要的投资者关系表单和相关链接。内容区域的结构主要是通过div和a标签来组织的。",
  "content_classification": "内容分类依据主要是链接的href属性，投资者关系内容主要包含财务报告和治理相关的链接，而新闻内容则集中在新闻和媒体报道的链接上。",
  "sample_links": {
    "investor": [
      "https://trisaluslifesci.com/financials/sec-filings",
      "https://trisaluslifesci.com/investor-resources/email-alerts",
      "https://trisaluslifesci.com/governance/governance-overview",
      "https://trisaluslifesci.com/news-events/press-releases"
    ],
    "news": [
      "https://trisaluslifesci.com/news-and-events/",
      "https://trisaluslifesci.com/press-media/",
      "https://trisaluslifesci.com/news-events/events-presentations"
    ]
  }
}
```
2025-07-03 04:55:20,224 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 04:55:20,224 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/investor-resources/email-alerts 提取到: 投资者4个, 新闻3个, 通用0个XPath规则
2025-07-03 04:55:21,226 - src.core.search_research - INFO - 分析investor_relations页面: https://investors.trisaluslifesci.com/
2025-07-03 04:55:21,226 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/
2025-07-03 04:55:21,226 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/
2025-07-03 04:55:26,308 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: / (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/
2025-07-03 04:55:27,315 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/
2025-07-03 04:55:32,410 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: / (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/
2025-07-03 04:55:32,410 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/
2025-07-03 04:55:32,410 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/
2025-07-03 04:55:40,018 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/, 内容长度: 188326
2025-07-03 04:55:40,018 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 188,326 字符
2025-07-03 04:55:40,152 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 04:55:40,152 - src.services.web_scraper - INFO -   原始长度: 188,326 字符
2025-07-03 04:55:40,152 - src.services.web_scraper - INFO -   清理后长度: 33,470 字符
2025-07-03 04:55:40,152 - src.services.web_scraper - INFO -   减少比例: 82.2%
2025-07-03 04:55:40,152 - src.services.web_scraper - INFO -   清理统计: 移除15个标签, 89个属性, 13个注释, 34个空标签
2025-07-03 04:55:40,152 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/, 内容长度: 33470
2025-07-03 04:55:40,152 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 33,470 字符
2025-07-03 04:55:40,221 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 04:55:40,221 - src.services.web_scraper - INFO -   原始长度: 33,470 字符
2025-07-03 04:55:40,221 - src.services.web_scraper - INFO -   清理后长度: 32,991 字符
2025-07-03 04:55:40,221 - src.services.web_scraper - INFO -   减少比例: 1.4%
2025-07-03 04:55:40,221 - src.services.web_scraper - INFO -   清理统计: 移除0个标签, 0个属性, 0个注释, 3个空标签
2025-07-03 04:55:40,221 - src.core.search_research - INFO - 页面内容适中(32,991字符)，使用单次处理: https://investors.trisaluslifesci.com/
2025-07-03 04:55:40,222 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 04:55:58,868 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 2063
2025-07-03 04:55:58,868 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 04:55:58,868 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_xpath_rules": [
    "//div[contains(@class, 'block--nir-assets__widget')]//a[contains(@href, '/news-releases')]",
    "//div[contains(@class, 'block--nir-assets__widget')]//a[contains(@href, '/financials')]",
    "//div[contains(@class, 'block--nir-assets__widget')]//a[contains(@href, '/sec-filings')]"
  ],
  "news_xpath_rules": [
    "//div[contains(@class, 'block--news-grid')]//a[contains(@href, '/news-releases')]",
    "//div[contains(@class, 'c-press-feed')]//a[contains(@href, '/news-releases')]",
    "//section[contains(@class, 'nir-widget--list')]//a[contains(@href, '/news-releases')]"
  ],
  "general_xpath_rules": [
    "//div[contains(@class, 'c-quicklinks')]//a[contains(@href, '/news-events')]",
    "//section[contains(@class, 'c-calltoaction')]//a[contains(@href, '/news-events')]"
  ],
  "primary_xpath": {
    "investor": "//div[contains(@class, 'block--nir-assets__widget')]//a[contains(@href, '/news-releases')]",
    "news": "//div[contains(@class, 'block--news-grid')]//a[contains(@href, '/news-releases')]",
    "general": "//div[contains(@class, 'c-quicklinks')]//a[contains(@href, '/news-events')]"
  },
  "confidence": 0.85,
  "page_structure": "页面包含多个主要区域，包括导航栏、投资者关系部分、新闻发布部分和快速链接部分。投资者关系部分包含财务报告和SEC文件的链接，新闻部分包含最新新闻和事件的链接。每个部分的内容通过特定的class和结构进行区分。",
  "content_classification": "内容分类依据页面的结构和链接的特征，投资者关系内容主要集中在财务和治理相关的链接，而新闻内容则集中在公司新闻和产品发布等信息上。",
  "sample_links": {
    "investor": [
      "https://trisaluslifesci.com/news-events/press-releases",
      "https://trisaluslifesci.com/financials/sec-filings",
      "https://trisaluslifesci.com/news-events/events-presentations"
    ],
    "news": [
      "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-commencement-exchange-offer-0",
      "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-launch-trinav-flx-infusion",
      "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-chief-financial-officer"
    ]
  }
}
```
2025-07-03 04:55:58,868 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 04:55:58,868 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/ 提取到: 投资者3个, 新闻3个, 通用2个XPath规则
2025-07-03 04:55:59,882 - src.core.search_research - INFO - 分析news_section页面: https://trisaluslifesci.com/news-and-events/
2025-07-03 04:55:59,882 - src.services.web_scraper - INFO - 开始抓取页面: https://trisaluslifesci.com/news-and-events/
2025-07-03 04:55:59,882 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://trisaluslifesci.com/news-and-events/
2025-07-03 04:56:00,012 - src.services.web_scraper - WARNING - requests页面抓取失败，状态码: 403, URL: https://trisaluslifesci.com/news-and-events/
2025-07-03 04:56:01,023 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://trisaluslifesci.com/news-and-events/
2025-07-03 04:56:01,149 - src.services.web_scraper - WARNING - requests页面抓取失败，状态码: 403, URL: https://trisaluslifesci.com/news-and-events/
2025-07-03 04:56:01,149 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://trisaluslifesci.com/news-and-events/
2025-07-03 04:56:01,149 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://trisaluslifesci.com/news-and-events/
2025-07-03 04:56:10,478 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://trisaluslifesci.com/news-and-events/, 内容长度: 205493
2025-07-03 04:56:10,478 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 205,493 字符
2025-07-03 04:56:10,687 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 04:56:10,687 - src.services.web_scraper - INFO -   原始长度: 205,493 字符
2025-07-03 04:56:10,687 - src.services.web_scraper - INFO -   清理后长度: 84,303 字符
2025-07-03 04:56:10,687 - src.services.web_scraper - INFO -   减少比例: 59.0%
2025-07-03 04:56:10,687 - src.services.web_scraper - INFO -   清理统计: 移除51个标签, 711个属性, 2个注释, 111个空标签
2025-07-03 04:56:10,687 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://trisaluslifesci.com/news-and-events/, 内容长度: 84303
2025-07-03 04:56:10,688 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 84,303 字符
2025-07-03 04:56:10,809 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 04:56:10,809 - src.services.web_scraper - INFO -   原始长度: 84,303 字符
2025-07-03 04:56:10,809 - src.services.web_scraper - INFO -   清理后长度: 83,324 字符
2025-07-03 04:56:10,809 - src.services.web_scraper - INFO -   减少比例: 1.2%
2025-07-03 04:56:10,809 - src.services.web_scraper - INFO -   清理统计: 移除0个标签, 0个属性, 0个注释, 9个空标签
2025-07-03 04:56:10,809 - src.core.search_research - INFO - 页面内容过长(83,324字符)，使用分批处理: https://trisaluslifesci.com/news-and-events/
2025-07-03 04:56:10,809 - src.services.web_scraper - INFO - HTML内容过长(83,324字符)，开始智能分割(阈值: 50,000)
2025-07-03 04:56:10,869 - src.services.web_scraper - INFO - 使用选择器 'section' 成功分割为 3 个分片
2025-07-03 04:56:10,869 - src.services.web_scraper - INFO - HTML内容分割完成，共生成 3 个分片
2025-07-03 04:56:10,869 - src.services.ai_analyzer - INFO - 开始分批分析分类XPath规则，页面类型: news_section，共 3 个分片
2025-07-03 04:56:10,869 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 04:56:18,687 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 2050
2025-07-03 04:56:18,687 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 04:56:18,687 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_xpath_rules": [
    "//ul[contains(@class, 'elementor-nav-menu') and contains(@id, 'sm-1751489763153325-12')]//a[contains(@href, 'financials')]",
    "//ul[contains(@class, 'elementor-nav-menu') and contains(@id, 'sm-1751489763153325-10')]//a[contains(@href, 'governance')]",
    "//ul[contains(@class, 'elementor-nav-menu') and contains(@id, 'sm-1751489763153325-14')]//a[contains(@href, 'stock-information')]"
  ],
  "news_xpath_rules": [
    "//ul[contains(@class, 'elementor-nav-menu') and contains(@id, 'sm-1751489763153325-8')]//a[contains(@href, 'press-releases')]",
    "//ul[contains(@class, 'elementor-nav-menu') and contains(@id, 'sm-1751489763153325-2')]//a[contains(@href, 'news-and-events')]",
    "//ul[contains(@class, 'elementor-nav-menu') and contains(@id, 'sm-1751489763153325-4')]//a[contains(@href, 'press-media')]"
  ],
  "general_xpath_rules": [
    "//div[contains(@class, 'elementor-widget-container')]//a[@href]",
    "//section[contains(@class, 'elementor-section')]//a[@href]"
  ],
  "primary_xpath": {
    "investor": "//ul[contains(@class, 'elementor-nav-menu') and contains(@id, 'sm-1751489763153325-12')]//a[contains(@href, 'financials')]",
    "news": "//ul[contains(@class, 'elementor-nav-menu') and contains(@id, 'sm-1751489763153325-8')]//a[contains(@href, 'press-releases')]",
    "general": "//div[contains(@class, 'elementor-widget-container')]//a[@href]"
  },
  "confidence": 0.85,
  "page_structure": "该页面主要由多个<section>和<div>元素组成，包含多个导航菜单和子菜单。新闻和投资者关系信息通过<ul>和<li>结构组织，链接通过<a>标签提供。页面使用了Elementor构建，包含多个类和ID用于样式和功能。",
  "content_classification": "内容分类依据主要是通过链接的href属性和包含的文本内容来区分投资者关系信息和一般新闻信息。投资者关系链接通常包含'financials'、'governance'等关键词，而新闻链接则包含'news'、'press'等关键词。",
  "sample_links": {
    "investor": [
      "https://investors.trisaluslifesci.com/financials/sec-filings",
      "https://investors.trisaluslifesci.com/governance/governance-overview"
    ],
    "news": [
      "https://trisaluslifesci.com/news-and-events/",
      "https://trisaluslifesci.com/press-media/"
    ]
  }
}
```
2025-07-03 04:56:18,687 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 04:56:18,687 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 04:56:27,395 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 1582
2025-07-03 04:56:27,395 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 04:56:27,395 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_xpath_rules": [
    "//div[contains(@class, 'news-feed')]//h3/a[@href]",
    "//section[@id='events']//h4/a[@href]",
    "//div[@class='elementor-widget-container']//a[contains(@href, 'investors.trisaluslifesci.com')]"
  ],
  "news_xpath_rules": [
    "//div[contains(@class, 'news-feed')]//h3/a[@href]",
    "//section[contains(@class, 'has_ae_slider')]//h4/a[@href]",
    "//h2[contains(text(), 'Recent Press Releases')]/following-sibling::div//h3/a[@href]"
  ],
  "general_xpath_rules": [
    "//section[contains(@class, 'has_ae_slider')]//a[@href]",
    "//div[contains(@class, 'elementor-widget-container')]//a[@href]"
  ],
  "primary_xpath": {
    "investor": "//div[contains(@class, 'news-feed')]//h3/a[@href]",
    "news": "//div[contains(@class, 'news-feed')]//h3/a[@href]",
    "general": "//section[contains(@class, 'has_ae_slider')]//a[@href]"
  },
  "confidence": 0.85,
  "page_structure": "页面包含多个<section>元素，每个<section>包含不同的内容区域。主要的新闻内容位于具有特定class的<div>中，新闻标题使用<h3>标签，链接在<a>标签中。投资者相关内容则在特定的<div>和<section>中，通常包含财务报告和新闻发布的链接。",
  "content_classification": "内容分类依据主要是HTML结构和class属性。新闻内容通常在具有'news-feed'的<div>中，而投资者信息则在包含'investor'或'financial'相关内容的区域。",
  "sample_links": {
    "investor": [
      "https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-commencement-exchange-offer-0",
      "https://investors.trisaluslifesci.com/news-events/press-releases"
    ],
    "news": [
      "https://trisaluslifesci.com/news-and-events/",
      "https://trisaluslifesci.com/press-media/"
    ]
  }
}
```
2025-07-03 04:56:27,396 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 04:56:27,396 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 04:56:31,454 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 1281
2025-07-03 04:56:31,454 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 04:56:31,454 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_xpath_rules": [],
  "news_xpath_rules": [
    "//section[contains(@class, 'elementor-section')]//h4//following-sibling::p//a[@href]",
    "//section[contains(@class, 'elementor-section')]//h4//a[@href]",
    "//section[contains(@class, 'elementor-section')]//h4[contains(@class, 'elementor-heading-title')]//ancestor::div[contains(@class, 'elementor-widget')]//following-sibling::div[contains(@class, 'elementor-widget-text-editor')]//a[@href]"
  ],
  "general_xpath_rules": [],
  "primary_xpath": {
    "investor": "",
    "news": "//section[contains(@class, 'elementor-section')]//h4//following-sibling::p//a[@href]",
    "general": ""
  },
  "confidence": 0.9,
  "page_structure": "页面包含多个<section>元素，每个<section>代表一个新闻条目，包含日期、标题、图片和文本内容。标题通常在<h4>标签中，链接通常在<p>标签内或直接在<h4>标签内。",
  "content_classification": "页面主要包含公司新闻和活动信息，符合一般新闻页面的特征，未发现投资者关系相关内容。",
  "sample_links": {
    "investor": [],
    "news": [
      "https://trisaluslifesci.com/recent-news/trisalus-life-sciences-to-showcase-treatment-platform-and-present-pressure-enabled-regional-immuno-oncology-perio-data-at-the-society-of-interventional-radiologys-2023-annual-scientific-mee/",
      "http://www.sio-central.org/p/cm/ld/fid=811",
      "https://conferences.asco.org/gi/attend"
    ]
  }
}
```
2025-07-03 04:56:31,454 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 04:56:31,455 - src.services.ai_analyzer - INFO - 分批分类XPath分析完成:
2025-07-03 04:56:31,455 - src.services.ai_analyzer - INFO -   投资者XPath: 6 -> 4 个唯一规则
2025-07-03 04:56:31,455 - src.services.ai_analyzer - INFO -   新闻XPath: 9 -> 4 个唯一规则
2025-07-03 04:56:31,455 - src.services.ai_analyzer - INFO -   通用XPath: 4 -> 2 个唯一规则
2025-07-03 04:56:31,455 - src.services.ai_analyzer - INFO -   平均置信度: 0.87
2025-07-03 04:56:31,455 - src.core.search_research - INFO - 从 https://trisaluslifesci.com/news-and-events/ 提取到: 投资者4个, 新闻4个, 通用2个XPath规则
2025-07-03 04:56:31,455 - src.core.search_research - INFO - 分批处理结果: {'total_fragments': 3, 'valid_results': 3, 'total_investor_found': 6, 'total_news_found': 9, 'total_general_found': 4, 'unique_investor': 4, 'unique_news': 4, 'unique_general': 2}
2025-07-03 04:56:32,455 - src.core.search_research - INFO - 分析news_section页面: https://trisaluslifesci.com/press-media/
2025-07-03 04:56:32,455 - src.services.web_scraper - INFO - 开始抓取页面: https://trisaluslifesci.com/press-media/
2025-07-03 04:56:32,455 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://trisaluslifesci.com/press-media/
2025-07-03 04:56:32,588 - src.services.web_scraper - WARNING - requests页面抓取失败，状态码: 403, URL: https://trisaluslifesci.com/press-media/
2025-07-03 04:56:33,601 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://trisaluslifesci.com/press-media/
2025-07-03 04:56:33,677 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='trisaluslifesci.com', port=443): Max retries exceeded with url: /press-media/ (Caused by ProtocolError('Connection aborted.', FileNotFoundError(2, 'No such file or directory'))), URL: https://trisaluslifesci.com/press-media/
2025-07-03 04:56:33,677 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://trisaluslifesci.com/press-media/
2025-07-03 04:56:33,677 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://trisaluslifesci.com/press-media/
2025-07-03 04:56:41,828 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://trisaluslifesci.com/press-media/, 内容长度: 193750
2025-07-03 04:56:41,828 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 193,750 字符
2025-07-03 04:56:41,997 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 04:56:41,997 - src.services.web_scraper - INFO -   原始长度: 193,750 字符
2025-07-03 04:56:41,997 - src.services.web_scraper - INFO -   清理后长度: 35,090 字符
2025-07-03 04:56:41,997 - src.services.web_scraper - INFO -   减少比例: 81.9%
2025-07-03 04:56:41,997 - src.services.web_scraper - INFO -   清理统计: 移除8个标签, 112个属性, 6个注释, 27个空标签
2025-07-03 04:56:41,997 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://trisaluslifesci.com/press-media/, 内容长度: 35090
2025-07-03 04:56:41,998 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 35,090 字符
2025-07-03 04:56:42,072 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 04:56:42,073 - src.services.web_scraper - INFO -   原始长度: 35,090 字符
2025-07-03 04:56:42,073 - src.services.web_scraper - INFO -   清理后长度: 34,611 字符
2025-07-03 04:56:42,073 - src.services.web_scraper - INFO -   减少比例: 1.4%
2025-07-03 04:56:42,073 - src.services.web_scraper - INFO -   清理统计: 移除0个标签, 0个属性, 0个注释, 3个空标签
2025-07-03 04:56:42,073 - src.core.search_research - INFO - 页面内容适中(34,611字符)，使用单次处理: https://trisaluslifesci.com/press-media/
2025-07-03 04:56:42,073 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 04:56:47,231 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 1416
2025-07-03 04:56:47,231 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 04:56:47,231 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_xpath_rules": [],
  "news_xpath_rules": [
    "//article[contains(@class, 'node--nir-news')]//a[@href]",
    "//div[@class='nir-widget--list']//article//a[@href]",
    "//div[@class='nir-widget--content']//a[contains(@href, '/news-releases')]"
  ],
  "general_xpath_rules": [],
  "primary_xpath": {
    "investor": "",
    "news": "//article[contains(@class, 'node--nir-news')]//a[@href]",
    "general": ""
  },
  "confidence": 0.9,
  "page_structure": "该页面主要由导航栏、新闻发布部分和页脚组成。新闻发布部分包含多个新闻条目，每个条目由一个包含标题和链接的<a>标签组成，位于<article>标签内。每个新闻条目还包含发布日期和摘要信息，结构上使用了<div>和<article>标签。",
  "content_classification": "页面主要包含新闻发布内容，未发现投资者关系相关的内容。新闻内容通过新闻条目的标题、发布日期和摘要进行分类。",
  "sample_links": {
    "investor": [],
    "news": [
      "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-commencement-exchange-offer-0",
      "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-launch-trinav-flx-infusion",
      "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-chief-financial-officer",
      "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-reports-first-quarter-2025-results-and",
      "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-220-million-private-placement"
    ]
  }
}
```
2025-07-03 04:56:47,231 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 04:56:47,231 - src.core.search_research - INFO - 从 https://trisaluslifesci.com/press-media/ 提取到: 投资者0个, 新闻3个, 通用0个XPath规则
2025-07-03 04:56:48,248 - src.core.search_research - INFO - 分析news_section页面: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-commencement-exchange-offer-0
2025-07-03 04:56:48,248 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-commencement-exchange-offer-0
2025-07-03 04:56:48,248 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-commencement-exchange-offer-0
2025-07-03 04:56:53,363 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /news-releases/news-release-details/trisalus-life-sciences-announces-commencement-exchange-offer-0 (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-commencement-exchange-offer-0
2025-07-03 04:56:54,368 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-commencement-exchange-offer-0
2025-07-03 04:56:59,446 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /news-releases/news-release-details/trisalus-life-sciences-announces-commencement-exchange-offer-0 (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-commencement-exchange-offer-0
2025-07-03 04:56:59,446 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-commencement-exchange-offer-0
2025-07-03 04:56:59,446 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-commencement-exchange-offer-0
2025-07-03 04:57:07,669 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-commencement-exchange-offer-0, 内容长度: 176136
2025-07-03 04:57:07,669 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 176,136 字符
2025-07-03 04:57:07,764 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 04:57:07,764 - src.services.web_scraper - INFO -   原始长度: 176,136 字符
2025-07-03 04:57:07,764 - src.services.web_scraper - INFO -   清理后长度: 30,713 字符
2025-07-03 04:57:07,764 - src.services.web_scraper - INFO -   减少比例: 82.6%
2025-07-03 04:57:07,764 - src.services.web_scraper - INFO -   清理统计: 移除8个标签, 69个属性, 5个注释, 15个空标签
2025-07-03 04:57:07,764 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-commencement-exchange-offer-0, 内容长度: 30713
2025-07-03 04:57:07,764 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 30,713 字符
2025-07-03 04:57:07,822 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 04:57:07,822 - src.services.web_scraper - INFO -   原始长度: 30,713 字符
2025-07-03 04:57:07,822 - src.services.web_scraper - INFO -   清理后长度: 30,234 字符
2025-07-03 04:57:07,822 - src.services.web_scraper - INFO -   减少比例: 1.6%
2025-07-03 04:57:07,822 - src.services.web_scraper - INFO -   清理统计: 移除0个标签, 0个属性, 0个注释, 3个空标签
2025-07-03 04:57:07,822 - src.core.search_research - INFO - 页面内容适中(30,234字符)，使用单次处理: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-commencement-exchange-offer-0
2025-07-03 04:57:07,823 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 04:57:11,683 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 830
2025-07-03 04:57:11,683 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 04:57:11,683 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_xpath_rules": [],
  "news_xpath_rules": [
    "//article[@class='node node--nir-news--full']//h2//div[@class='field__item']/a",
    "//div[@class='node__content']//p//a[contains(@href, '/node/')]",
    "//div[@class='full-release-body']//p//a[contains(@href, 'businesswire.com')]"
  ],
  "general_xpath_rules": [],
  "primary_xpath": {
    "investor": "",
    "news": "//article[@class='node node--nir-news--full']//h2//div[@class='field__item']/a",
    "general": ""
  },
  "confidence": 0.9,
  "page_structure": "页面主要由一个包含新闻详细信息的article元素构成，包含新闻标题、发布日期、正文内容和相关链接。新闻标题在h2标签内，发布日期和正文内容在div和p标签中。",
  "content_classification": "页面主要集中在新闻发布内容，包含新闻标题、发布日期和详细信息，没有投资者关系相关内容。",
  "sample_links": {
    "investor": [],
    "news": [
      "https://www.businesswire.com/news/home/<USER>/en/"
    ]
  }
}
```
2025-07-03 04:57:11,683 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 04:57:11,683 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-commencement-exchange-offer-0 提取到: 投资者0个, 新闻3个, 通用0个XPath规则
2025-07-03 04:57:12,684 - src.core.search_research - INFO - 分析news_section页面: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-launch-trinav-flx-infusion
2025-07-03 04:57:12,684 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-launch-trinav-flx-infusion
2025-07-03 04:57:12,684 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-launch-trinav-flx-infusion
2025-07-03 04:57:17,761 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /news-releases/news-release-details/trisalus-life-sciences-announces-launch-trinav-flx-infusion (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-launch-trinav-flx-infusion
2025-07-03 04:57:18,776 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-launch-trinav-flx-infusion
2025-07-03 04:57:23,882 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /news-releases/news-release-details/trisalus-life-sciences-announces-launch-trinav-flx-infusion (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-launch-trinav-flx-infusion
2025-07-03 04:57:23,882 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-launch-trinav-flx-infusion
2025-07-03 04:57:23,882 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-launch-trinav-flx-infusion
2025-07-03 04:57:31,925 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-launch-trinav-flx-infusion, 内容长度: 170234
2025-07-03 04:57:31,926 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 170,234 字符
2025-07-03 04:57:32,023 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 04:57:32,023 - src.services.web_scraper - INFO -   原始长度: 170,234 字符
2025-07-03 04:57:32,023 - src.services.web_scraper - INFO -   清理后长度: 25,153 字符
2025-07-03 04:57:32,023 - src.services.web_scraper - INFO -   减少比例: 85.2%
2025-07-03 04:57:32,023 - src.services.web_scraper - INFO -   清理统计: 移除8个标签, 69个属性, 5个注释, 16个空标签
2025-07-03 04:57:32,023 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-launch-trinav-flx-infusion, 内容长度: 25153
2025-07-03 04:57:32,023 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 25,153 字符
2025-07-03 04:57:32,092 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 04:57:32,092 - src.services.web_scraper - INFO -   原始长度: 25,153 字符
2025-07-03 04:57:32,092 - src.services.web_scraper - INFO -   清理后长度: 24,674 字符
2025-07-03 04:57:32,092 - src.services.web_scraper - INFO -   减少比例: 1.9%
2025-07-03 04:57:32,092 - src.services.web_scraper - INFO -   清理统计: 移除0个标签, 0个属性, 0个注释, 3个空标签
2025-07-03 04:57:32,092 - src.core.search_research - INFO - 页面内容适中(24,674字符)，使用单次处理: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-launch-trinav-flx-infusion
2025-07-03 04:57:32,092 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 04:57:36,009 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 910
2025-07-03 04:57:36,009 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 04:57:36,009 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_xpath_rules": [],
  "news_xpath_rules": [
    "//article[@class='node node--nir-news--full']//h2//div[@class='field__item']/a",
    "//div[@class='node__content']//p/a[contains(@href, 'businesswire.com')]",
    "//div[@class='node__content']//p/a[contains(@href, 'trinavinfusion.com')]"
  ],
  "general_xpath_rules": [],
  "primary_xpath": {
    "investor": "",
    "news": "//article[@class='node node--nir-news--full']//h2//div[@class='field__item']/a",
    "general": ""
  },
  "confidence": 0.9,
  "page_structure": "该页面主要由一个包含新闻发布详细信息的article元素组成，包含新闻标题、发布日期、正文内容和相关链接。新闻标题在h2标签内，发布日期和其他信息在div标签中，正文内容在node__content类的div中。",
  "content_classification": "该页面主要包含公司新闻发布的信息，属于一般新闻页面。没有投资者关系相关的内容，因此没有投资者XPath规则。",
  "sample_links": {
    "investor": [],
    "news": [
      "https://www.businesswire.com/news/home/<USER>/en/",
      "https://trinavinfusion.com/"
    ]
  }
}
```
2025-07-03 04:57:36,009 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 04:57:36,009 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-launch-trinav-flx-infusion 提取到: 投资者0个, 新闻3个, 通用0个XPath规则
2025-07-03 04:57:37,017 - src.core.search_research - INFO - 分析news_section页面: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-chief-financial-officer
2025-07-03 04:57:37,017 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-chief-financial-officer
2025-07-03 04:57:37,017 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-chief-financial-officer
2025-07-03 04:57:42,110 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /news-releases/news-release-details/trisalus-life-sciences-announces-chief-financial-officer (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-chief-financial-officer
2025-07-03 04:57:43,113 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-chief-financial-officer
2025-07-03 04:57:48,209 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /news-releases/news-release-details/trisalus-life-sciences-announces-chief-financial-officer (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-chief-financial-officer
2025-07-03 04:57:48,209 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-chief-financial-officer
2025-07-03 04:57:48,209 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-chief-financial-officer
2025-07-03 04:57:56,213 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-chief-financial-officer, 内容长度: 168872
2025-07-03 04:57:56,213 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 168,872 字符
2025-07-03 04:57:56,313 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 04:57:56,314 - src.services.web_scraper - INFO -   原始长度: 168,872 字符
2025-07-03 04:57:56,314 - src.services.web_scraper - INFO -   清理后长度: 23,876 字符
2025-07-03 04:57:56,314 - src.services.web_scraper - INFO -   减少比例: 85.9%
2025-07-03 04:57:56,314 - src.services.web_scraper - INFO -   清理统计: 移除8个标签, 69个属性, 5个注释, 15个空标签
2025-07-03 04:57:56,314 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-chief-financial-officer, 内容长度: 23876
2025-07-03 04:57:56,314 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 23,876 字符
2025-07-03 04:57:56,365 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 04:57:56,365 - src.services.web_scraper - INFO -   原始长度: 23,876 字符
2025-07-03 04:57:56,365 - src.services.web_scraper - INFO -   清理后长度: 23,397 字符
2025-07-03 04:57:56,365 - src.services.web_scraper - INFO -   减少比例: 2.0%
2025-07-03 04:57:56,365 - src.services.web_scraper - INFO -   清理统计: 移除0个标签, 0个属性, 0个注释, 3个空标签
2025-07-03 04:57:56,365 - src.core.search_research - INFO - 页面内容适中(23,397字符)，使用单次处理: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-chief-financial-officer
2025-07-03 04:57:56,366 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 04:57:59,988 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 892
2025-07-03 04:57:59,988 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 04:57:59,988 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_xpath_rules": [],
  "news_xpath_rules": [
    "//article[@class='node node--nir-news--full']//h2//div[@class='field__item']/a",
    "//div[@id='lfg-main-content']//article//h2//div[@class='field__item']/a",
    "//div[@class='full-release-body']//p//a[contains(@href, 'businesswire.com')]"
  ],
  "general_xpath_rules": [],
  "primary_xpath": {
    "investor": "",
    "news": "//article[@class='node node--nir-news--full']//h2//div[@class='field__item']/a",
    "general": ""
  },
  "confidence": 0.9,
  "page_structure": "该页面主要由一个包含新闻详细信息的article元素构成，包含标题、发布日期、正文内容和相关链接。新闻标题在h2标签内，发布日期和其他信息在div标签中，正文内容在full-release-body类的div中。",
  "content_classification": "该页面主要是关于TriSalus Life Sciences的新闻发布，内容包括新闻标题、发布日期和详细的新闻内容，符合一般新闻页面的特征。",
  "sample_links": {
    "investor": [],
    "news": [
      "https://www.businesswire.com/news/home/<USER>/en/"
    ]
  }
}
```
2025-07-03 04:57:59,988 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 04:57:59,988 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-chief-financial-officer 提取到: 投资者0个, 新闻3个, 通用0个XPath规则
2025-07-03 04:58:00,990 - src.core.search_research - INFO - 分类XPath规则提取完成:
2025-07-03 04:58:00,990 - src.core.search_research - INFO -   投资者XPath: 32 个
2025-07-03 04:58:00,990 - src.core.search_research - INFO -   新闻XPath: 32 个
2025-07-03 04:58:00,990 - src.core.search_research - INFO -   通用XPath: 12 个
2025-07-03 04:58:00,990 - src.core.search_research - INFO - 提取到投资者XPath规则: 32个
2025-07-03 04:58:00,990 - src.core.search_research - INFO - 提取到新闻XPath规则: 32个
2025-07-03 04:58:00,990 - src.core.search_research - INFO - 提取到通用XPath规则: 12个
2025-07-03 04:58:00,990 - src.core.search_research - INFO - 总计XPath规则: 76个
2025-07-03 04:58:00,990 - src.core.search_research - INFO - 公司调研完成: TriSalus
2025-07-03 04:58:02,016 - src.services.drission_scraper - INFO - DrissionPage浏览器实例已关闭
2025-07-03 04:58:02,016 - src.services.web_scraper - INFO - DrissionPage资源已清理
2025-07-03 04:58:02,016 - __main__ - INFO - ✓ TriSalus 调研完成
2025-07-03 04:58:02,017 - __main__ - INFO - 调研结果已保存到: research_results.json
2025-07-03 04:58:02,018 - __main__ - INFO - 所有公司调研完成
2025-07-03 08:13:29,895 - __main__ - INFO - AI Tools Find News - 公司调研工具启动
2025-07-03 08:13:29,895 - __main__ - INFO - 准备调研 1 个公司
2025-07-03 08:13:29,895 - __main__ - INFO - 开始调研第 1/1 个公司: TriSalus
2025-07-03 08:13:29,895 - src.services.google_search - INFO - Google搜索服务初始化完成
2025-07-03 08:13:29,895 - src.services.web_scraper - INFO - 网页抓取服务初始化完成
2025-07-03 08:13:29,953 - src.services.ai_analyzer - INFO - AI分析服务初始化完成
2025-07-03 08:13:29,953 - src.core.search_research - INFO - SearchResearchClass 初始化完成
2025-07-03 08:13:29,953 - src.core.search_research - INFO - 开始调研公司: TriSalus
2025-07-03 08:13:29,957 - src.core.search_research - INFO - 步骤1: 搜索公司官网
2025-07-03 08:13:29,957 - src.services.google_search - INFO - 执行Google搜索: TriSalus
2025-07-03 08:13:32,776 - src.services.google_search - INFO - 搜索成功，返回 10 个结果
2025-07-03 08:13:32,777 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 08:13:35,319 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 272
2025-07-03 08:13:35,319 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 08:13:35,319 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "official_website": "https://trisaluslifesci.com/",
  "confidence": 0.98,
  "reasoning": "选择了https://trisaluslifesci.com/作为TriSalus的官方网站，因为该URL直接对应于公司的名称'Trisalus Life Sciences'，并且是一个主流的.com域名。该页面的标题和内容都明确表明这是TriSalus的官方网站，且没有涉及第三方平台或子页面，因此具有较高的权威性和相关性。"
}
```
2025-07-03 08:13:35,319 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 08:13:35,319 - src.core.search_research - INFO - 找到官网: https://trisaluslifesci.com/
2025-07-03 08:13:35,319 - src.core.search_research - INFO - 步骤2: 分析官网首页，发掘新闻板块
2025-07-03 08:13:35,319 - src.services.web_scraper - INFO - 开始抓取页面: https://trisaluslifesci.com/
2025-07-03 08:13:35,319 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://trisaluslifesci.com/
2025-07-03 08:13:35,697 - src.services.web_scraper - WARNING - requests页面抓取失败，状态码: 403, URL: https://trisaluslifesci.com/
2025-07-03 08:13:36,711 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://trisaluslifesci.com/
2025-07-03 08:13:36,896 - src.services.web_scraper - WARNING - requests页面抓取失败，状态码: 403, URL: https://trisaluslifesci.com/
2025-07-03 08:13:36,896 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://trisaluslifesci.com/
2025-07-03 08:13:37,249 - src.services.drission_scraper - INFO - DrissionPage抓取服务初始化完成
2025-07-03 08:13:37,249 - src.services.web_scraper - INFO - DrissionPage抓取服务已初始化
2025-07-03 08:13:37,250 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://trisaluslifesci.com/
2025-07-03 08:13:38,190 - src.services.drission_scraper - INFO - ChromiumPage实例创建成功
2025-07-03 08:13:47,506 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://trisaluslifesci.com/, 内容长度: 276752
2025-07-03 08:13:47,506 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 276,752 字符
2025-07-03 08:13:47,843 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 08:13:47,843 - src.services.web_scraper - INFO -   原始长度: 276,752 字符
2025-07-03 08:13:47,843 - src.services.web_scraper - INFO -   清理后长度: 131,608 字符
2025-07-03 08:13:47,843 - src.services.web_scraper - INFO -   减少比例: 52.4%
2025-07-03 08:13:47,844 - src.services.web_scraper - INFO -   清理统计: 移除53个标签, 1247个属性, 2个注释, 216个空标签
2025-07-03 08:13:47,844 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://trisaluslifesci.com/, 内容长度: 131608
2025-07-03 08:13:47,844 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 131,608 字符
2025-07-03 08:13:48,045 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 08:13:48,046 - src.services.web_scraper - INFO -   原始长度: 131,608 字符
2025-07-03 08:13:48,046 - src.services.web_scraper - INFO -   清理后长度: 130,588 字符
2025-07-03 08:13:48,046 - src.services.web_scraper - INFO -   减少比例: 0.8%
2025-07-03 08:13:48,046 - src.services.web_scraper - INFO -   清理统计: 移除0个标签, 0个属性, 0个注释, 9个空标签
2025-07-03 08:13:48,046 - src.core.search_research - INFO - HTML内容过长(130,588字符)，使用分批处理
2025-07-03 08:13:48,046 - src.services.web_scraper - INFO - HTML内容过长(130,588字符)，开始智能分割(阈值: 50,000)
2025-07-03 08:13:48,138 - src.services.web_scraper - INFO - 使用选择器 'section' 成功分割为 4 个分片
2025-07-03 08:13:48,138 - src.services.web_scraper - INFO - HTML内容分割完成，共生成 4 个分片
2025-07-03 08:13:48,139 - src.services.ai_analyzer - INFO - 开始分批分析新闻板块，共 4 个分片
2025-07-03 08:13:48,139 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 08:13:54,030 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 1312
2025-07-03 08:13:54,030 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 08:13:54,030 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_relations_urls": [
    "https://investors.trisaluslifesci.com/investor-relations",
    "https://investors.trisaluslifesci.com/news-events/press-releases",
    "https://investors.trisaluslifesci.com/news-events/events-presentations",
    "https://investors.trisaluslifesci.com/governance/governance-overview",
    "https://investors.trisaluslifesci.com/financials/sec-filings",
    "https://investors.trisaluslifesci.com/financials/quarterly-results",
    "https://investors.trisaluslifesci.com/financials/annual-reports",
    "https://investors.trisaluslifesci.com/stock-information",
    "https://investors.trisaluslifesci.com/investor-resources/email-alerts"
  ],
  "news_section_urls": [
    "https://trisaluslifesci.com/news-and-events/",
    "https://trisaluslifesci.com/press-media/"
  ],
  "mixed_section_urls": [],
  "found_keywords": {
    "investor": ["投资者关系", "财务信息", "年报", "季报", "SEC文件", "治理"],
    "news": ["新闻", "Press", "媒体", "公告", "动态"],
    "mixed": []
  },
  "confidence": 0.95,
  "classification_notes": "分类依据主要是链接的文本内容和URL路径。投资者关系链接包含了与投资者相关的所有信息，如财务报告、治理结构等。一般新闻链接则包含了新闻和媒体相关的信息。没有找到符合混合板块的链接，因为所有链接都明确归入了投资者关系或一般新闻。",
  "structure_analysis": "页面结构主要由主导航菜单构成，包含多个子菜单，分别指向不同的内容区域。投资者关系和新闻信息均在主导航中有明确的分类，且各自的链接清晰可见。投资者关系链接集中在'Investors'部分，而新闻链接则在'News & Events'部分。"
}
```
2025-07-03 08:13:54,030 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 08:13:54,032 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 08:13:59,447 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 1561
2025-07-03 08:13:59,447 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 08:13:59,447 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_relations_urls": [
    "https://investors.trisaluslifesci.com/",
    "https://investors.trisaluslifesci.com/news-events/press-releases",
    "https://investors.trisaluslifesci.com/news-events/events-presentations",
    "https://investors.trisaluslifesci.com/governance/governance-overview",
    "https://investors.trisaluslifesci.com/governance/committee-composition",
    "https://investors.trisaluslifesci.com/governance/board-diversity-matrix",
    "https://investors.trisaluslifesci.com/financials/sec-filings",
    "https://investors.trisaluslifesci.com/financials/quarterly-results",
    "https://investors.trisaluslifesci.com/financials/annual-reports",
    "https://investors.trisaluslifesci.com/stock-information",
    "https://investors.trisaluslifesci.com/stock-information/analyst-coverage",
    "https://investors.trisaluslifesci.com/investor-resources/email-alerts",
    "https://investors.trisaluslifesci.com/investor-resources/investor-contacts"
  ],
  "news_section_urls": [
    "https://trisaluslifesci.com/news-and-events/",
    "https://trisaluslifesci.com/press-media/"
  ],
  "mixed_section_urls": [],
  "found_keywords": {
    "investor": ["投资者关系", "财务信息", "治理"],
    "news": ["新闻", "Press", "媒体"],
    "mixed": []
  },
  "confidence": 0.95,
  "classification_notes": "在分析过程中，所有链接均根据其文本和URL进行分类。投资者关系链接包含了明确的投资者相关信息，如财务报告、治理结构等。一般新闻链接则包含了新闻和媒体相关的内容。没有找到符合混合板块的链接，因为所有新闻链接均为一般新闻，且没有同时包含投资者信息的链接。",
  "structure_analysis": "页面结构主要由主导航菜单构成，包含多个子菜单，分别指向投资者关系和新闻相关的链接。投资者关系链接集中在一个子菜单中，而新闻链接则在另一个子菜单中。整体结构清晰，便于用户导航。"
}
```
2025-07-03 08:13:59,447 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 08:13:59,448 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 08:14:04,803 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 1630
2025-07-03 08:14:04,803 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 08:14:04,803 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_relations_urls": [
    "https://investors.trisaluslifesci.com/",
    "https://investors.trisaluslifesci.com/news-events/press-releases",
    "https://investors.trisaluslifesci.com/governance/governance-overview",
    "https://investors.trisaluslifesci.com/financials/sec-filings",
    "https://investors.trisaluslifesci.com/financials/quarterly-results",
    "https://investors.trisaluslifesci.com/financials/annual-reports",
    "https://investors.trisaluslifesci.com/stock-information",
    "https://investors.trisaluslifesci.com/investor-resources/email-alerts"
  ],
  "news_section_urls": [
    "https://trisaluslifesci.com/news-and-events/",
    "https://trisaluslifesci.com/press-media/",
    "https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-commencement-exchange-offer-0",
    "https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-launch-trinav-flx-infusion",
    "https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-chief-financial-officer"
  ],
  "mixed_section_urls": [],
  "found_keywords": {
    "investor": ["投资者关系", "财务信息", "治理"],
    "news": ["新闻", "Press", "媒体"],
    "mixed": []
  },
  "confidence": 0.95,
  "classification_notes": "分类依据主要是链接文本和URL路径的关键词分析。投资者关系链接包含了明确的投资者相关信息，如财务报告、治理结构等；一般新闻链接则包含了新闻发布和媒体相关内容。没有找到符合混合板块的链接，因为所有链接要么属于投资者关系，要么属于一般新闻。",
  "structure_analysis": "页面结构主要由主导航菜单和内容区域组成，主导航中包含了投资者和新闻相关的链接。投资者关系链接集中在'/investors'路径下，而新闻链接则在'/news-and-events'和'/press-media'等路径下。内容区域中也包含了最新的新闻动态，进一步支持了分类的准确性。"
}
```
2025-07-03 08:14:04,803 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 08:14:04,804 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 08:14:07,725 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 540
2025-07-03 08:14:07,725 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 08:14:07,725 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_relations_urls": [],
  "news_section_urls": [],
  "mixed_section_urls": [
    "https://trisaluslifesci.com/pipeline/"
  ],
  "found_keywords": {
    "investor": [],
    "news": [],
    "mixed": []
  },
  "confidence": 0.7,
  "classification_notes": "在提供的HTML内容中，没有找到明确的投资者关系或一般新闻的链接。唯一找到的链接是指向'PIPELINE'页面，虽然该页面可能包含一些与公司动态相关的信息，但没有明确的新闻或投资者关系内容，因此将其归类为混合板块。没有找到符合投资者关系或一般新闻的关键词。",
  "structure_analysis": "页面结构主要由一个包含多个子元素的<section>构成，包含了关于公司管道的信息。没有明显的导航菜单或页脚链接，且内容主要集中在'PIPELINE'的介绍上。没有提供其他链接或信息来指向投资者关系或新闻板块。"
}
```
2025-07-03 08:14:07,725 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 08:14:07,726 - src.services.ai_analyzer - INFO - 分批新闻板块分析完成:
2025-07-03 08:14:07,727 - src.services.ai_analyzer - INFO -   投资者关系: 30 -> 14 个唯一URL
2025-07-03 08:14:07,727 - src.services.ai_analyzer - INFO -   新闻板块: 9 -> 5 个唯一URL
2025-07-03 08:14:07,727 - src.services.ai_analyzer - INFO -   混合板块: 1 -> 0 个唯一URL
2025-07-03 08:14:07,727 - src.services.ai_analyzer - INFO -   平均置信度: 0.89
2025-07-03 08:14:07,727 - src.core.search_research - INFO - 分批处理结果: {'total_fragments': 4, 'valid_results': 4, 'total_investor_found': 30, 'total_news_found': 9, 'total_mixed_found': 1, 'unique_investor': 14, 'unique_news': 5, 'unique_mixed': 0}
2025-07-03 08:14:07,727 - src.core.search_research - INFO - 找到投资者关系页面: 14个
2025-07-03 08:14:07,727 - src.core.search_research - INFO - 找到一般新闻页面: 5个
2025-07-03 08:14:07,727 - src.core.search_research - INFO - 找到混合板块页面: 0个
2025-07-03 08:14:07,727 - src.core.search_research - INFO - 步骤3: 分析新闻板块页面，提取分类XPath规则
2025-07-03 08:14:07,733 - src.core.search_research - INFO - 分析investor_relations页面: https://investors.trisaluslifesci.com/investor-relations
2025-07-03 08:14:07,733 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/investor-relations
2025-07-03 08:14:07,733 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/investor-relations
2025-07-03 08:14:13,234 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /investor-relations (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/investor-relations
2025-07-03 08:14:14,235 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/investor-relations
2025-07-03 08:14:19,349 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /investor-relations (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/investor-relations
2025-07-03 08:14:19,349 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/investor-relations
2025-07-03 08:14:19,349 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/investor-relations
2025-07-03 08:14:28,911 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/investor-relations, 内容长度: 188727
2025-07-03 08:14:28,911 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 188,727 字符
2025-07-03 08:14:29,051 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 08:14:29,051 - src.services.web_scraper - INFO -   原始长度: 188,727 字符
2025-07-03 08:14:29,051 - src.services.web_scraper - INFO -   清理后长度: 33,470 字符
2025-07-03 08:14:29,051 - src.services.web_scraper - INFO -   减少比例: 82.3%
2025-07-03 08:14:29,051 - src.services.web_scraper - INFO -   清理统计: 移除16个标签, 89个属性, 13个注释, 34个空标签
2025-07-03 08:14:29,051 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/investor-relations, 内容长度: 33470
2025-07-03 08:14:29,051 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 33,470 字符
2025-07-03 08:14:29,119 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 08:14:29,119 - src.services.web_scraper - INFO -   原始长度: 33,470 字符
2025-07-03 08:14:29,119 - src.services.web_scraper - INFO -   清理后长度: 32,991 字符
2025-07-03 08:14:29,119 - src.services.web_scraper - INFO -   减少比例: 1.4%
2025-07-03 08:14:29,119 - src.services.web_scraper - INFO -   清理统计: 移除0个标签, 0个属性, 0个注释, 3个空标签
2025-07-03 08:14:29,120 - src.core.search_research - INFO - 页面内容适中(32,991字符)，使用单次处理: https://investors.trisaluslifesci.com/investor-relations
2025-07-03 08:14:29,120 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 08:14:39,293 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 1899
2025-07-03 08:14:39,293 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 08:14:39,293 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_xpath_rules": [
    "//div[contains(@class, 'block--nir-assets__widget')]//a[contains(@href, '/news-releases')]",
    "//div[contains(@class, 'block--nir-assets__widget')]//a[contains(@href, '/financials')]",
    "//div[contains(@class, 'block--nir-assets__widget')]//a[contains(@href, '/sec-filings')]"
  ],
  "news_xpath_rules": [
    "//div[contains(@class, 'c-press-feed')]//a[@href]",
    "//div[contains(@class, 'nir-widget--news--headline')]//a[@href]",
    "//section[contains(@class, 'block--news-grid')]//a[@href]"
  ],
  "general_xpath_rules": [
    "//div[contains(@class, 'c-quicklinks')]//a[@href]",
    "//section[contains(@class, 'c-calltoaction')]//a[@href]"
  ],
  "primary_xpath": {
    "investor": "//div[contains(@class, 'block--nir-assets__widget')]//a[contains(@href, '/news-releases')]",
    "news": "//div[contains(@class, 'c-press-feed')]//a[@href]",
    "general": "//div[contains(@class, 'c-quicklinks')]//a[@href]"
  },
  "confidence": 0.85,
  "page_structure": "页面包含多个主要部分，包括导航栏、投资者关系内容、新闻发布、财务报告和股东信息等。主要内容区域通过不同的class和section标签进行区分，新闻和投资者信息分别在不同的div和section中组织。",
  "content_classification": "内容分类依据主要是通过class名称和HTML结构来区分投资者关系内容和新闻内容。投资者关系内容通常包含财务报告和SEC文件的链接，而新闻内容则包含公司新闻和产品发布的信息。",
  "sample_links": {
    "investor": [
      "https://trisaluslifesci.com/news-events/press-releases",
      "https://trisaluslifesci.com/financials/sec-filings",
      "https://trisaluslifesci.com/news-events/events-presentations"
    ],
    "news": [
      "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-commencement-exchange-offer-0",
      "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-launch-trinav-flx-infusion",
      "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-chief-financial-officer"
    ]
  }
}
```
2025-07-03 08:14:39,293 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 08:14:39,293 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/investor-relations 提取到: 投资者3个, 新闻3个, 通用2个XPath规则
2025-07-03 08:14:40,305 - src.core.search_research - INFO - 分析investor_relations页面: https://investors.trisaluslifesci.com/news-events/press-releases
2025-07-03 08:14:40,305 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/news-events/press-releases
2025-07-03 08:14:40,305 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/news-events/press-releases
2025-07-03 08:14:45,427 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /news-events/press-releases (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/news-events/press-releases
2025-07-03 08:14:46,439 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/news-events/press-releases
2025-07-03 08:14:51,558 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /news-events/press-releases (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/news-events/press-releases
2025-07-03 08:14:51,558 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/news-events/press-releases
2025-07-03 08:14:51,558 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/news-events/press-releases
2025-07-03 08:14:59,952 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/news-events/press-releases, 内容长度: 194151
2025-07-03 08:14:59,953 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 194,151 字符
2025-07-03 08:15:00,118 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 08:15:00,118 - src.services.web_scraper - INFO -   原始长度: 194,151 字符
2025-07-03 08:15:00,118 - src.services.web_scraper - INFO -   清理后长度: 35,090 字符
2025-07-03 08:15:00,118 - src.services.web_scraper - INFO -   减少比例: 81.9%
2025-07-03 08:15:00,118 - src.services.web_scraper - INFO -   清理统计: 移除9个标签, 112个属性, 6个注释, 27个空标签
2025-07-03 08:15:00,119 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/news-events/press-releases, 内容长度: 35090
2025-07-03 08:15:00,119 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 35,090 字符
2025-07-03 08:15:00,192 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 08:15:00,192 - src.services.web_scraper - INFO -   原始长度: 35,090 字符
2025-07-03 08:15:00,192 - src.services.web_scraper - INFO -   清理后长度: 34,611 字符
2025-07-03 08:15:00,192 - src.services.web_scraper - INFO -   减少比例: 1.4%
2025-07-03 08:15:00,193 - src.services.web_scraper - INFO -   清理统计: 移除0个标签, 0个属性, 0个注释, 3个空标签
2025-07-03 08:15:00,193 - src.core.search_research - INFO - 页面内容适中(34,611字符)，使用单次处理: https://investors.trisaluslifesci.com/news-events/press-releases
2025-07-03 08:15:00,193 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 08:15:05,295 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 1566
2025-07-03 08:15:05,295 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 08:15:05,295 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_xpath_rules": [],
  "news_xpath_rules": [
    "//article[contains(@class, 'node--nir-news--nir-widget-list')]//a[contains(@href, '/news-releases/')]",
    "//div[@class='nir-widget--list']//a[contains(@href, '/news-releases/')]",
    "//div[@class='nir-widget--content']//a[contains(@href, '/news-releases/')]"
  ],
  "general_xpath_rules": [],
  "primary_xpath": {
    "investor": "",
    "news": "//article[contains(@class, 'node--nir-news--nir-widget-list')]//a[contains(@href, '/news-releases/')]",
    "general": ""
  },
  "confidence": 0.9,
  "page_structure": "该页面主要由一个包含新闻发布的列表组成，使用了多个<article>元素，每个元素包含新闻标题、发布日期和摘要。新闻链接以<a>标签形式存在，通常包含'/news-releases/'路径。页面还包含导航栏和页脚，但这些部分不包含新闻链接。",
  "content_classification": "页面主要包含新闻发布信息，未包含投资者关系相关内容，因此分类为一般新闻页面。",
  "sample_links": {
    "investor": [],
    "news": [
      "https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-commencement-exchange-offer-0",
      "https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-launch-trinav-flx-infusion",
      "https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-chief-financial-officer",
      "https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-reports-first-quarter-2025-results-and",
      "https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-220-million-private-placement"
    ]
  }
}
```
2025-07-03 08:15:05,295 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 08:15:05,295 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/news-events/press-releases 提取到: 投资者0个, 新闻3个, 通用0个XPath规则
2025-07-03 08:15:06,310 - src.core.search_research - INFO - 分析investor_relations页面: https://investors.trisaluslifesci.com/news-events/events-presentations
2025-07-03 08:15:06,310 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/news-events/events-presentations
2025-07-03 08:15:06,310 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/news-events/events-presentations
2025-07-03 08:15:11,406 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /news-events/events-presentations (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/news-events/events-presentations
2025-07-03 08:15:12,407 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/news-events/events-presentations
2025-07-03 08:15:17,516 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /news-events/events-presentations (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/news-events/events-presentations
2025-07-03 08:15:17,516 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/news-events/events-presentations
2025-07-03 08:15:17,516 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/news-events/events-presentations
2025-07-03 08:15:26,192 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/news-events/events-presentations, 内容长度: 180921
2025-07-03 08:15:26,192 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 180,921 字符
2025-07-03 08:15:26,329 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 08:15:26,329 - src.services.web_scraper - INFO -   原始长度: 180,921 字符
2025-07-03 08:15:26,329 - src.services.web_scraper - INFO -   清理后长度: 29,940 字符
2025-07-03 08:15:26,329 - src.services.web_scraper - INFO -   减少比例: 83.5%
2025-07-03 08:15:26,330 - src.services.web_scraper - INFO -   清理统计: 移除8个标签, 83个属性, 6个注释, 17个空标签
2025-07-03 08:15:26,330 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/news-events/events-presentations, 内容长度: 29940
2025-07-03 08:15:26,330 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 29,940 字符
2025-07-03 08:15:26,400 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 08:15:26,400 - src.services.web_scraper - INFO -   原始长度: 29,940 字符
2025-07-03 08:15:26,400 - src.services.web_scraper - INFO -   清理后长度: 29,461 字符
2025-07-03 08:15:26,400 - src.services.web_scraper - INFO -   减少比例: 1.6%
2025-07-03 08:15:26,400 - src.services.web_scraper - INFO -   清理统计: 移除0个标签, 0个属性, 0个注释, 3个空标签
2025-07-03 08:15:26,400 - src.core.search_research - INFO - 页面内容适中(29,461字符)，使用单次处理: https://investors.trisaluslifesci.com/news-events/events-presentations
2025-07-03 08:15:26,401 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 08:15:33,544 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 2038
2025-07-03 08:15:33,545 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 08:15:33,545 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_xpath_rules": [
    "//div[@class='nir-widget--field nir-widget--asset--list-of-files']//a[@href]",
    "//div[@class='nir-widget--field nir-widget--event--title']//a[@href]",
    "//div[@class='nir-widget--field nir-widget--event--webcast']//a[@href]"
  ],
  "news_xpath_rules": [
    "//article[@class='node node--nir-event--nir-widget-list']//div[@class='field-nir-event-title']//a[@href]",
    "//div[@class='nir-widget--field nir-widget--event--title']//a[@href]",
    "//div[@class='nir-widget--field nir-widget--event--date']//following-sibling::div[@class='nir-widget--field nir-widget--event--title']//a[@href]"
  ],
  "general_xpath_rules": [
    "//div[@class='nir-widget llf-container-fluid-full']//a[@href]",
    "//div[@class='nir-widget--content']//a[@href]"
  ],
  "primary_xpath": {
    "investor": "//div[@class='nir-widget--field nir-widget--asset--list-of-files']//a[@href]",
    "news": "//article[@class='node node--nir-event--nir-widget-list']//div[@class='field-nir-event-title']//a[@href]",
    "general": "//div[@class='nir-widget llf-container-fluid-full']//a[@href]"
  },
  "confidence": 0.85,
  "page_structure": "页面包含多个section，其中包含投资者关系和新闻事件的内容。主要内容区域通过class和id进行区分，投资者相关内容集中在特定的widget中，而新闻内容则在不同的article标签中呈现。每个内容项通常包含标题、日期和链接。",
  "content_classification": "内容分类依据主要是通过HTML结构中的class和标签类型进行区分，投资者关系内容通常包含财务报告和事件信息，而新闻内容则集中在公司新闻和事件公告中。",
  "sample_links": {
    "investor": [
      "https://edge.media-server.com/mmc/p/vqrjxogk",
      "https://edge.media-server.com/mmc/p/g6oxnay5",
      "https://edge.media-server.com/mmc/p/w7fp8yvn",
      "https://edge.media-server.com/mmc/p/2hmvqta7/",
      "https://edge.media-server.com/mmc/p/hvbgap5o"
    ],
    "news": [
      "https://trisaluslifesci.com/news-and-events/#events",
      "https://trisaluslifesci.com/news-and-events/#events",
      "https://trisaluslifesci.com/news-and-events/#events",
      "https://trisaluslifesci.com/news-and-events/#events",
      "https://trisaluslifesci.com/news-and-events/#events"
    ]
  }
}
```
2025-07-03 08:15:33,545 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 08:15:33,545 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/news-events/events-presentations 提取到: 投资者3个, 新闻3个, 通用2个XPath规则
2025-07-03 08:15:34,550 - src.core.search_research - INFO - 分析investor_relations页面: https://investors.trisaluslifesci.com/governance/governance-overview
2025-07-03 08:15:34,550 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/governance/governance-overview
2025-07-03 08:15:34,550 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/governance/governance-overview
2025-07-03 08:15:39,669 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /governance/governance-overview (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/governance/governance-overview
2025-07-03 08:15:40,684 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/governance/governance-overview
2025-07-03 08:15:45,823 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /governance/governance-overview (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/governance/governance-overview
2025-07-03 08:15:45,823 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/governance/governance-overview
2025-07-03 08:15:45,823 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/governance/governance-overview
2025-07-03 08:15:53,758 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/governance/governance-overview, 内容长度: 168988
2025-07-03 08:15:53,758 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 168,988 字符
2025-07-03 08:15:53,866 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 08:15:53,866 - src.services.web_scraper - INFO -   原始长度: 168,988 字符
2025-07-03 08:15:53,866 - src.services.web_scraper - INFO -   清理后长度: 22,278 字符
2025-07-03 08:15:53,867 - src.services.web_scraper - INFO -   减少比例: 86.8%
2025-07-03 08:15:53,867 - src.services.web_scraper - INFO -   清理统计: 移除8个标签, 74个属性, 6个注释, 23个空标签
2025-07-03 08:15:53,867 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/governance/governance-overview, 内容长度: 22278
2025-07-03 08:15:53,867 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 22,278 字符
2025-07-03 08:15:53,935 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 08:15:53,935 - src.services.web_scraper - INFO -   原始长度: 22,278 字符
2025-07-03 08:15:53,935 - src.services.web_scraper - INFO -   清理后长度: 21,799 字符
2025-07-03 08:15:53,935 - src.services.web_scraper - INFO -   减少比例: 2.2%
2025-07-03 08:15:53,935 - src.services.web_scraper - INFO -   清理统计: 移除0个标签, 0个属性, 0个注释, 3个空标签
2025-07-03 08:15:53,935 - src.core.search_research - INFO - 页面内容适中(21,799字符)，使用单次处理: https://investors.trisaluslifesci.com/governance/governance-overview
2025-07-03 08:15:53,936 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 08:15:59,734 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 1612
2025-07-03 08:15:59,734 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 08:15:59,734 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_xpath_rules": [
    "//div[@class='nir-widget--list']//a[contains(@href, '/static-files')]",
    "//div[@class='nir-widget--list']//a[contains(@href, '/financials')]",
    "//div[@class='nir-widget--list']//a[contains(@href, '/governance')]"
  ],
  "news_xpath_rules": [
    "//ul[@class='navbar-secondary-links']//a[contains(@href, '/news-and-events')]",
    "//div[@class='nir-widget--list']//a[contains(@href, '/press-media')]",
    "//div[@class='nir-widget--list']//a[contains(@href, '/news-events')]"
  ],
  "general_xpath_rules": [
    "//div[@class='region region-content']//a[@href]",
    "//section[@class='top-section']//a[@href]"
  ],
  "primary_xpath": {
    "investor": "//div[@class='nir-widget--list']//a[contains(@href, '/static-files')]",
    "news": "//ul[@class='navbar-secondary-links']//a[contains(@href, '/news-and-events')]",
    "general": "//div[@class='region region-content']//a[@href]"
  },
  "confidence": 0.85,
  "page_structure": "页面主要由导航栏、内容区域和页脚组成。内容区域包含多个部分，包括治理文档和委员会章程的链接。导航栏提供了对新闻和投资者关系的访问。",
  "content_classification": "内容分类依据主要是链接的URL结构和所在的HTML元素。投资者关系内容通常包含'/financials'或'/governance'，而新闻内容则包含'/news-and-events'或'/press-media'。",
  "sample_links": {
    "investor": [
      "https://trisaluslifesci.com/static-files/43e281ed-94d0-435e-90b7-ec78a928def7",
      "https://trisaluslifesci.com/static-files/62569002-5a5a-4aac-a4d4-59932b437b9a",
      "https://trisaluslifesci.com/governance/governance-overview"
    ],
    "news": [
      "https://trisaluslifesci.com/news-and-events/",
      "https://trisaluslifesci.com/press-media/"
    ]
  }
}
```
2025-07-03 08:15:59,734 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 08:15:59,734 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/governance/governance-overview 提取到: 投资者3个, 新闻3个, 通用2个XPath规则
2025-07-03 08:16:00,737 - src.core.search_research - INFO - 分析investor_relations页面: https://investors.trisaluslifesci.com/financials/sec-filings
2025-07-03 08:16:00,737 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/financials/sec-filings
2025-07-03 08:16:00,737 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/financials/sec-filings
2025-07-03 08:16:05,849 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /financials/sec-filings (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/financials/sec-filings
2025-07-03 08:16:06,854 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/financials/sec-filings
2025-07-03 08:16:11,966 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /financials/sec-filings (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/financials/sec-filings
2025-07-03 08:16:11,966 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/financials/sec-filings
2025-07-03 08:16:11,966 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/financials/sec-filings
2025-07-03 08:16:20,167 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/financials/sec-filings, 内容长度: 201005
2025-07-03 08:16:20,167 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 201,005 字符
2025-07-03 08:16:20,334 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 08:16:20,334 - src.services.web_scraper - INFO -   原始长度: 201,005 字符
2025-07-03 08:16:20,334 - src.services.web_scraper - INFO -   清理后长度: 36,893 字符
2025-07-03 08:16:20,334 - src.services.web_scraper - INFO -   减少比例: 81.6%
2025-07-03 08:16:20,334 - src.services.web_scraper - INFO -   清理统计: 移除8个标签, 139个属性, 6个注释, 52个空标签
2025-07-03 08:16:20,334 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/financials/sec-filings, 内容长度: 36893
2025-07-03 08:16:20,334 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 36,893 字符
2025-07-03 08:16:20,431 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 08:16:20,431 - src.services.web_scraper - INFO -   原始长度: 36,893 字符
2025-07-03 08:16:20,431 - src.services.web_scraper - INFO -   清理后长度: 36,414 字符
2025-07-03 08:16:20,431 - src.services.web_scraper - INFO -   减少比例: 1.3%
2025-07-03 08:16:20,431 - src.services.web_scraper - INFO -   清理统计: 移除0个标签, 0个属性, 0个注释, 3个空标签
2025-07-03 08:16:20,431 - src.core.search_research - INFO - 页面内容适中(36,414字符)，使用单次处理: https://investors.trisaluslifesci.com/financials/sec-filings
2025-07-03 08:16:20,431 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 08:16:26,188 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 1474
2025-07-03 08:16:26,188 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 08:16:26,188 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_xpath_rules": [
    "//table[@class='nirtable views-table views-view-table cols-4 collapse-table-wide']//td/a[contains(@href, '/sec-filings/sec-filing')]",
    "//table[@class='nirtable views-table views-view-table cols-4 collapse-table-wide']//td/a[contains(@href, '/financials/')]",
    "//div[@class='view-content']//a[contains(@href, '/financials/')]"
  ],
  "news_xpath_rules": [],
  "general_xpath_rules": [],
  "primary_xpath": {
    "investor": "//table[@class='nirtable views-table views-view-table cols-4 collapse-table-wide']//td/a[contains(@href, '/sec-filings/sec-filing')]",
    "news": "",
    "general": ""
  },
  "confidence": 0.9,
  "page_structure": "页面主要由一个表格组成，包含SEC文件的相关信息。表格的每一行代表一个文件，包含日期、文件类型、描述和查看链接。表格外部有一些导航和过滤器，但主要内容集中在表格内。",
  "content_classification": "页面主要集中在投资者关系内容，特别是SEC文件的列表，缺乏一般新闻内容的结构，因此没有找到相关的新闻XPath规则。",
  "sample_links": {
    "investor": [
      "https://investors.trisaluslifesci.com/financials/sec-filings/sec-filing/4/0001826667-25-000079",
      "https://investors.trisaluslifesci.com/financials/sec-filings/sec-filing/4/0001826667-25-000083",
      "https://investors.trisaluslifesci.com/financials/sec-filings/sec-filing/4/0001826667-25-000081",
      "https://investors.trisaluslifesci.com/financials/sec-filings/sec-filing/4/0001826667-25-000087",
      "https://investors.trisaluslifesci.com/financials/sec-filings/sec-filing/4/0001826667-25-000085"
    ],
    "news": [],
    "general": []
  }
}
```
2025-07-03 08:16:26,188 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 08:16:26,188 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/financials/sec-filings 提取到: 投资者3个, 新闻0个, 通用0个XPath规则
2025-07-03 08:16:27,196 - src.core.search_research - INFO - 分析investor_relations页面: https://investors.trisaluslifesci.com/financials/quarterly-results
2025-07-03 08:16:27,196 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/financials/quarterly-results
2025-07-03 08:16:27,196 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/financials/quarterly-results
2025-07-03 08:16:32,307 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /financials/quarterly-results (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/financials/quarterly-results
2025-07-03 08:16:33,322 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/financials/quarterly-results
2025-07-03 08:16:38,445 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /financials/quarterly-results (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/financials/quarterly-results
2025-07-03 08:16:38,445 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/financials/quarterly-results
2025-07-03 08:16:38,445 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/financials/quarterly-results
2025-07-03 08:16:46,485 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/financials/quarterly-results, 内容长度: 169317
2025-07-03 08:16:46,485 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 169,317 字符
2025-07-03 08:16:46,597 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 08:16:46,598 - src.services.web_scraper - INFO -   原始长度: 169,317 字符
2025-07-03 08:16:46,598 - src.services.web_scraper - INFO -   清理后长度: 22,137 字符
2025-07-03 08:16:46,598 - src.services.web_scraper - INFO -   减少比例: 86.9%
2025-07-03 08:16:46,598 - src.services.web_scraper - INFO -   清理统计: 移除8个标签, 77个属性, 6个注释, 19个空标签
2025-07-03 08:16:46,598 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/financials/quarterly-results, 内容长度: 22137
2025-07-03 08:16:46,598 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 22,137 字符
2025-07-03 08:16:46,661 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 08:16:46,662 - src.services.web_scraper - INFO -   原始长度: 22,137 字符
2025-07-03 08:16:46,662 - src.services.web_scraper - INFO -   清理后长度: 21,658 字符
2025-07-03 08:16:46,662 - src.services.web_scraper - INFO -   减少比例: 2.2%
2025-07-03 08:16:46,662 - src.services.web_scraper - INFO -   清理统计: 移除0个标签, 0个属性, 0个注释, 3个空标签
2025-07-03 08:16:46,662 - src.core.search_research - INFO - 页面内容适中(21,658字符)，使用单次处理: https://investors.trisaluslifesci.com/financials/quarterly-results
2025-07-03 08:16:46,662 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 08:16:52,819 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 1490
2025-07-03 08:16:52,819 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 08:16:52,819 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_xpath_rules": [
    "//div[@class='views-field views-field-field-nir-bundle-content']//a[contains(text(), 'Earnings Release')]",
    "//div[@class='views-field views-field-field-nir-bundle-content']//a[contains(text(), 'Form')]",
    "//section[contains(@class, 'block--views-blockwidget-bundled-content')]//a[@href]"
  ],
  "news_xpath_rules": [],
  "general_xpath_rules": [],
  "primary_xpath": {
    "investor": "//div[@class='views-field views-field-field-nir-bundle-content']//a[contains(text(), 'Earnings Release')]",
    "news": "",
    "general": ""
  },
  "confidence": 0.9,
  "page_structure": "页面主要由导航栏、财务结果部分和联系信息组成。财务结果部分包含多个季度的财务报告和SEC文件链接，使用了div和ul/li结构来组织内容。",
  "content_classification": "内容主要集中在投资者关系信息上，特别是财务报告和SEC文件，未包含一般新闻内容。",
  "sample_links": {
    "investor": [
      "https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-reports-q4-and-full-year-2024-financial",
      "https://investors.trisaluslifesci.com/sec-filings/sec-filing/nt-10-k/0001826667-25-000027",
      "https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-reports-q3-2024-financial-results-and-provides-business",
      "https://investors.trisaluslifesci.com/sec-filings/sec-filing/10-q/0001826667-24-000051",
      "https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-reports-q2-2024-financial-results-and-business-update"
    ],
    "news": [],
    "general": []
  }
}
```
2025-07-03 08:16:52,819 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 08:16:52,819 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/financials/quarterly-results 提取到: 投资者3个, 新闻0个, 通用0个XPath规则
2025-07-03 08:16:53,825 - src.core.search_research - INFO - 分析investor_relations页面: https://investors.trisaluslifesci.com/financials/annual-reports
2025-07-03 08:16:53,825 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/financials/annual-reports
2025-07-03 08:16:53,825 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/financials/annual-reports
2025-07-03 08:16:58,940 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /financials/annual-reports (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/financials/annual-reports
2025-07-03 08:16:59,942 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/financials/annual-reports
2025-07-03 08:17:05,058 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /financials/annual-reports (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/financials/annual-reports
2025-07-03 08:17:05,058 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/financials/annual-reports
2025-07-03 08:17:05,058 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/financials/annual-reports
2025-07-03 08:17:12,588 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/financials/annual-reports, 内容长度: 160080
2025-07-03 08:17:12,588 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 160,080 字符
2025-07-03 08:17:12,667 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 08:17:12,667 - src.services.web_scraper - INFO -   原始长度: 160,080 字符
2025-07-03 08:17:12,667 - src.services.web_scraper - INFO -   清理后长度: 15,826 字符
2025-07-03 08:17:12,667 - src.services.web_scraper - INFO -   减少比例: 90.1%
2025-07-03 08:17:12,667 - src.services.web_scraper - INFO -   清理统计: 移除8个标签, 68个属性, 6个注释, 16个空标签
2025-07-03 08:17:12,667 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/financials/annual-reports, 内容长度: 15826
2025-07-03 08:17:12,667 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 15,826 字符
2025-07-03 08:17:12,704 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 08:17:12,704 - src.services.web_scraper - INFO -   原始长度: 15,826 字符
2025-07-03 08:17:12,704 - src.services.web_scraper - INFO -   清理后长度: 15,347 字符
2025-07-03 08:17:12,704 - src.services.web_scraper - INFO -   减少比例: 3.0%
2025-07-03 08:17:12,704 - src.services.web_scraper - INFO -   清理统计: 移除0个标签, 0个属性, 0个注释, 3个空标签
2025-07-03 08:17:12,704 - src.core.search_research - INFO - 页面内容适中(15,347字符)，使用单次处理: https://investors.trisaluslifesci.com/financials/annual-reports
2025-07-03 08:17:12,704 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 08:17:17,900 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 1066
2025-07-03 08:17:17,900 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 08:17:17,900 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_xpath_rules": [
    "//div[@class='block--nir-assets__widget']//a[contains(@href, '/financials/annual-reports')]",
    "//div[@class='block--nir-assets__widget']//a[contains(@href, '/financials/sec-filings')]",
    "//div[@class='block--nir-assets__widget']//a[contains(@href, '/financials/quarterly-results')]"
  ],
  "news_xpath_rules": [],
  "general_xpath_rules": [],
  "primary_xpath": {
    "investor": "//div[@class='block--nir-assets__widget']//a[contains(@href, '/financials/annual-reports')]",
    "news": "",
    "general": ""
  },
  "confidence": 0.9,
  "page_structure": "该页面主要由导航栏、标题、内容区域和页脚组成。内容区域包含一个主要的投资者关系部分，专注于财务报告和相关链接。没有明显的新闻内容部分，主要是投资者相关信息。",
  "content_classification": "页面主要集中在投资者关系内容，包含财务报告和SEC文件的链接，没有一般新闻内容。",
  "sample_links": {
    "investor": [
      "https://investors.trisaluslifesci.com/financials/annual-reports",
      "https://investors.trisaluslifesci.com/financials/sec-filings",
      "https://investors.trisaluslifesci.com/financials/quarterly-results"
    ],
    "news": [],
    "general": []
  }
}
```
2025-07-03 08:17:17,900 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 08:17:17,901 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/financials/annual-reports 提取到: 投资者3个, 新闻0个, 通用0个XPath规则
2025-07-03 08:17:18,904 - src.core.search_research - INFO - 分析investor_relations页面: https://investors.trisaluslifesci.com/stock-information
2025-07-03 08:17:18,904 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/stock-information
2025-07-03 08:17:18,904 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/stock-information
2025-07-03 08:17:24,008 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /stock-information (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/stock-information
2025-07-03 08:17:25,013 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/stock-information
2025-07-03 08:17:30,118 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /stock-information (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/stock-information
2025-07-03 08:17:30,118 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/stock-information
2025-07-03 08:17:30,118 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/stock-information
2025-07-03 08:17:39,738 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/stock-information, 内容长度: 193189
2025-07-03 08:17:39,738 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 193,189 字符
2025-07-03 08:17:39,905 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 08:17:39,906 - src.services.web_scraper - INFO -   原始长度: 193,189 字符
2025-07-03 08:17:39,906 - src.services.web_scraper - INFO -   清理后长度: 33,124 字符
2025-07-03 08:17:39,906 - src.services.web_scraper - INFO -   减少比例: 82.9%
2025-07-03 08:17:39,906 - src.services.web_scraper - INFO -   清理统计: 移除10个标签, 148个属性, 6个注释, 48个空标签
2025-07-03 08:17:39,906 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/stock-information, 内容长度: 33124
2025-07-03 08:17:39,906 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 33,124 字符
2025-07-03 08:17:40,008 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 08:17:40,008 - src.services.web_scraper - INFO -   原始长度: 33,124 字符
2025-07-03 08:17:40,008 - src.services.web_scraper - INFO -   清理后长度: 32,645 字符
2025-07-03 08:17:40,008 - src.services.web_scraper - INFO -   减少比例: 1.4%
2025-07-03 08:17:40,008 - src.services.web_scraper - INFO -   清理统计: 移除0个标签, 0个属性, 0个注释, 3个空标签
2025-07-03 08:17:40,008 - src.core.search_research - INFO - 页面内容适中(32,645字符)，使用单次处理: https://investors.trisaluslifesci.com/stock-information
2025-07-03 08:17:40,008 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 08:17:46,923 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 2130
2025-07-03 08:17:46,923 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 08:17:46,923 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_xpath_rules": [
    "//ul[@class='navbar-nav navbar-secondary-links']//a[contains(text(), 'Press Releases')]",
    "//ul[@class='navbar-nav navbar-secondary-links']//a[contains(text(), 'Events & Presentations')]",
    "//ul[@class='navbar-nav navbar-secondary-links']//a[contains(text(), 'SEC Filings')]",
    "//ul[@class='navbar-nav navbar-secondary-links']//a[contains(text(), 'Quarterly Results')]",
    "//ul[@class='navbar-nav navbar-secondary-links']//a[contains(text(), 'Annual Reports')]"
  ],
  "news_xpath_rules": [
    "//div[@class='navbar-nav navbar-secondary-links']//a[contains(text(), 'Recent News & Events')]",
    "//ul[@class='navbar-nav navbar-secondary-links']//a[contains(text(), 'Press & Media')]",
    "//article[@class='node node--type-nir_landing_page']//h1/following-sibling::div//a"
  ],
  "general_xpath_rules": [
    "//div[@class='navbar-nav navbar-primary-links']//a[@href]",
    "//div[@class='navbar-nav navbar-secondary-links']//a[@href]"
  ],
  "primary_xpath": {
    "investor": "//ul[@class='navbar-nav navbar-secondary-links']//a[contains(text(), 'Press Releases')]",
    "news": "//div[@class='navbar-nav navbar-secondary-links']//a[contains(text(), 'Recent News & Events')]",
    "general": "//div[@class='navbar-nav navbar-primary-links']//a[@href]"
  },
  "confidence": 0.85,
  "page_structure": "页面主要由导航栏、内容区域和页脚组成。导航栏包含多个下拉菜单，分别指向投资者关系和新闻相关的链接。内容区域包含股票信息、历史数据和投资计算器等模块。每个模块通过不同的class和id进行区分。",
  "content_classification": "内容分类依据主要是链接文本和所在的HTML结构。投资者关系内容主要集中在包含'Press Releases'、'Events & Presentations'等文本的链接，而新闻内容则集中在'Recent News & Events'和'Press & Media'等链接。",
  "sample_links": {
    "investor": [
      "https://trisaluslifesci.com/news-events/press-releases",
      "https://trisaluslifesci.com/news-events/events-presentations",
      "https://trisaluslifesci.com/financials/sec-filings",
      "https://trisaluslifesci.com/financials/quarterly-results",
      "https://trisaluslifesci.com/financials/annual-reports"
    ],
    "news": [
      "https://trisaluslifesci.com/news-and-events/",
      "https://trisaluslifesci.com/press-media/"
    ]
  }
}
```
2025-07-03 08:17:46,923 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 08:17:46,923 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/stock-information 提取到: 投资者5个, 新闻3个, 通用2个XPath规则
2025-07-03 08:17:47,936 - src.core.search_research - INFO - 分析investor_relations页面: https://investors.trisaluslifesci.com/investor-resources/email-alerts
2025-07-03 08:17:47,936 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/investor-resources/email-alerts
2025-07-03 08:17:47,936 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/investor-resources/email-alerts
2025-07-03 08:17:53,065 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /investor-resources/email-alerts (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/investor-resources/email-alerts
2025-07-03 08:17:54,070 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/investor-resources/email-alerts
2025-07-03 08:17:59,187 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /investor-resources/email-alerts (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/investor-resources/email-alerts
2025-07-03 08:17:59,187 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/investor-resources/email-alerts
2025-07-03 08:17:59,187 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/investor-resources/email-alerts
2025-07-03 08:18:08,362 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/investor-resources/email-alerts, 内容长度: 171319
2025-07-03 08:18:08,362 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 171,319 字符
2025-07-03 08:18:08,451 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 08:18:08,451 - src.services.web_scraper - INFO -   原始长度: 171,319 字符
2025-07-03 08:18:08,451 - src.services.web_scraper - INFO -   清理后长度: 20,872 字符
2025-07-03 08:18:08,451 - src.services.web_scraper - INFO -   减少比例: 87.8%
2025-07-03 08:18:08,451 - src.services.web_scraper - INFO -   清理统计: 移除11个标签, 103个属性, 6个注释, 20个空标签
2025-07-03 08:18:08,451 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/investor-resources/email-alerts, 内容长度: 20872
2025-07-03 08:18:08,451 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 20,872 字符
2025-07-03 08:18:08,497 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 08:18:08,497 - src.services.web_scraper - INFO -   原始长度: 20,872 字符
2025-07-03 08:18:08,497 - src.services.web_scraper - INFO -   清理后长度: 20,393 字符
2025-07-03 08:18:08,497 - src.services.web_scraper - INFO -   减少比例: 2.3%
2025-07-03 08:18:08,497 - src.services.web_scraper - INFO -   清理统计: 移除0个标签, 0个属性, 0个注释, 3个空标签
2025-07-03 08:18:08,497 - src.core.search_research - INFO - 页面内容适中(20,393字符)，使用单次处理: https://investors.trisaluslifesci.com/investor-resources/email-alerts
2025-07-03 08:18:08,498 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 08:18:13,018 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 1500
2025-07-03 08:18:13,019 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 08:18:13,019 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_xpath_rules": [
    "//ul[contains(@class, 'navbar-secondary-links')]//a[contains(@href, '/financials')]",
    "//ul[contains(@class, 'navbar-secondary-links')]//a[contains(@href, '/investor-resources')]",
    "//ul[contains(@class, 'navbar-secondary-links')]//a[contains(@href, '/governance')]"
  ],
  "news_xpath_rules": [
    "//ul[contains(@class, 'navbar-secondary-links')]//a[contains(@href, '/news-and-events')]",
    "//ul[contains(@class, 'navbar-secondary-links')]//a[contains(@href, '/press-media')]"
  ],
  "general_xpath_rules": [],
  "primary_xpath": {
    "investor": "//ul[contains(@class, 'navbar-secondary-links')]//a[contains(@href, '/financials')]",
    "news": "//ul[contains(@class, 'navbar-secondary-links')]//a[contains(@href, '/news-and-events')]",
    "general": ""
  },
  "confidence": 0.85,
  "page_structure": "页面包含一个导航栏，主要分为两个部分：投资者关系和新闻与事件。投资者关系部分包含财务报告、治理文档等链接，新闻与事件部分包含公司新闻和媒体报道链接。页面的主要内容区域是一个表单，用于用户注册电子邮件提醒。",
  "content_classification": "内容分类依据主要是链接的href属性，投资者关系链接通常包含'/financials'、'/investor-resources'和'/governance'等路径，而新闻链接则包含'/news-and-events'和'/press-media'等路径。",
  "sample_links": {
    "investor": [
      "https://trisaluslifesci.com/financials/sec-filings",
      "https://trisaluslifesci.com/investor-resources/email-alerts",
      "https://trisaluslifesci.com/governance/governance-overview"
    ],
    "news": [
      "https://trisaluslifesci.com/news-and-events/",
      "https://trisaluslifesci.com/press-media/"
    ]
  }
}
```
2025-07-03 08:18:13,019 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 08:18:13,019 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/investor-resources/email-alerts 提取到: 投资者3个, 新闻2个, 通用0个XPath规则
2025-07-03 08:18:14,031 - src.core.search_research - INFO - 分析investor_relations页面: https://investors.trisaluslifesci.com/
2025-07-03 08:18:14,031 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/
2025-07-03 08:18:14,031 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/
2025-07-03 08:18:19,147 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: / (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/
2025-07-03 08:18:20,162 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/
2025-07-03 08:18:25,302 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: / (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/
2025-07-03 08:18:25,302 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/
2025-07-03 08:18:25,302 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/
2025-07-03 08:18:33,650 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/, 内容长度: 188326
2025-07-03 08:18:33,651 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 188,326 字符
2025-07-03 08:18:33,790 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 08:18:33,790 - src.services.web_scraper - INFO -   原始长度: 188,326 字符
2025-07-03 08:18:33,790 - src.services.web_scraper - INFO -   清理后长度: 33,470 字符
2025-07-03 08:18:33,790 - src.services.web_scraper - INFO -   减少比例: 82.2%
2025-07-03 08:18:33,790 - src.services.web_scraper - INFO -   清理统计: 移除15个标签, 89个属性, 13个注释, 34个空标签
2025-07-03 08:18:33,790 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/, 内容长度: 33470
2025-07-03 08:18:33,790 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 33,470 字符
2025-07-03 08:18:33,861 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 08:18:33,861 - src.services.web_scraper - INFO -   原始长度: 33,470 字符
2025-07-03 08:18:33,861 - src.services.web_scraper - INFO -   清理后长度: 32,991 字符
2025-07-03 08:18:33,861 - src.services.web_scraper - INFO -   减少比例: 1.4%
2025-07-03 08:18:33,861 - src.services.web_scraper - INFO -   清理统计: 移除0个标签, 0个属性, 0个注释, 3个空标签
2025-07-03 08:18:33,862 - src.core.search_research - INFO - 页面内容适中(32,991字符)，使用单次处理: https://investors.trisaluslifesci.com/
2025-07-03 08:18:33,862 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 08:18:46,225 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 1823
2025-07-03 08:18:46,226 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 08:18:46,226 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_xpath_rules": [
    "//div[contains(@class, 'block--nir-assets__widget')]//a[contains(@href, '/news-releases')]",
    "//div[contains(@class, 'block--nir-assets__widget')]//a[contains(@href, '/financials')]",
    "//div[contains(@class, 'block--nir-assets__widget')]//a[contains(@href, '/sec-filings')]"
  ],
  "news_xpath_rules": [
    "//div[contains(@class, 'c-press-feed')]//a[@href]",
    "//div[contains(@class, 'nir-widget--news--headline')]//a[@href]",
    "//section[contains(@class, 'block--news-grid')]//a[@href]"
  ],
  "general_xpath_rules": [
    "//div[contains(@class, 'c-quicklinks')]//a[@href]",
    "//section[contains(@class, 'c-calltoaction')]//a[@href]"
  ],
  "primary_xpath": {
    "investor": "//div[contains(@class, 'block--nir-assets__widget')]//a[contains(@href, '/news-releases')]",
    "news": "//div[contains(@class, 'c-press-feed')]//a[@href]",
    "general": "//div[contains(@class, 'c-quicklinks')]//a[@href]"
  },
  "confidence": 0.85,
  "page_structure": "页面主要由多个section和div组成，包含投资者关系信息和新闻内容。投资者信息通常在包含'block--nir-assets__widget'的div中，而新闻内容则在'c-press-feed'类的div中。页面还包含快速链接和行动呼吁部分，提供额外的导航。",
  "content_classification": "内容分类依据主要是通过class属性区分不同类型的内容区域。投资者关系内容通常涉及财务报告和SEC文件，而新闻内容则涉及公司新闻和产品发布。",
  "sample_links": {
    "investor": [
      "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-commencement-exchange-offer-0",
      "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-launch-trinav-flx-infusion",
      "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-chief-financial-officer"
    ],
    "news": [
      "https://trisaluslifesci.com/news-and-events/",
      "https://trisaluslifesci.com/press-media/"
    ]
  }
}
```
2025-07-03 08:18:46,226 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 08:18:46,227 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/ 提取到: 投资者3个, 新闻3个, 通用2个XPath规则
2025-07-03 08:18:47,231 - src.core.search_research - INFO - 分析investor_relations页面: https://investors.trisaluslifesci.com/governance/committee-composition
2025-07-03 08:18:47,231 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/governance/committee-composition
2025-07-03 08:18:47,231 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/governance/committee-composition
2025-07-03 08:18:52,341 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /governance/committee-composition (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/governance/committee-composition
2025-07-03 08:18:53,344 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/governance/committee-composition
2025-07-03 08:18:58,479 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /governance/committee-composition (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/governance/committee-composition
2025-07-03 08:18:58,479 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/governance/committee-composition
2025-07-03 08:18:58,479 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/governance/committee-composition
2025-07-03 08:19:06,200 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/governance/committee-composition, 内容长度: 167927
2025-07-03 08:19:06,200 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 167,927 字符
2025-07-03 08:19:06,296 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 08:19:06,297 - src.services.web_scraper - INFO -   原始长度: 167,927 字符
2025-07-03 08:19:06,297 - src.services.web_scraper - INFO -   清理后长度: 19,641 字符
2025-07-03 08:19:06,297 - src.services.web_scraper - INFO -   减少比例: 88.3%
2025-07-03 08:19:06,297 - src.services.web_scraper - INFO -   清理统计: 移除8个标签, 116个属性, 6个注释, 47个空标签
2025-07-03 08:19:06,297 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/governance/committee-composition, 内容长度: 19641
2025-07-03 08:19:06,297 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 19,641 字符
2025-07-03 08:19:06,348 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 08:19:06,348 - src.services.web_scraper - INFO -   原始长度: 19,641 字符
2025-07-03 08:19:06,348 - src.services.web_scraper - INFO -   清理后长度: 19,162 字符
2025-07-03 08:19:06,348 - src.services.web_scraper - INFO -   减少比例: 2.4%
2025-07-03 08:19:06,348 - src.services.web_scraper - INFO -   清理统计: 移除0个标签, 0个属性, 0个注释, 3个空标签
2025-07-03 08:19:06,348 - src.core.search_research - INFO - 页面内容适中(19,162字符)，使用单次处理: https://investors.trisaluslifesci.com/governance/committee-composition
2025-07-03 08:19:06,349 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 08:19:11,787 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 1821
2025-07-03 08:19:11,789 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 08:19:11,789 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_xpath_rules": [
    "//div[@class='navbar-nav navbar-secondary-links']//a[contains(text(), 'Press Releases')]",
    "//div[@class='navbar-nav navbar-secondary-links']//a[contains(text(), 'Events & Presentations')]",
    "//div[@class='navbar-nav navbar-secondary-links']//a[contains(text(), 'Governance Overview')]",
    "//div[@class='navbar-nav navbar-secondary-links']//a[contains(text(), 'Board Diversity Matrix')]",
    "//div[@class='navbar-nav navbar-secondary-links']//a[contains(text(), 'SEC Filings')]"
  ],
  "news_xpath_rules": [
    "//div[@class='navbar-nav navbar-secondary-links']//a[contains(text(), 'Recent News & Events')]",
    "//div[@class='navbar-nav navbar-secondary-links']//a[contains(text(), 'Press & Media')]"
  ],
  "general_xpath_rules": [],
  "primary_xpath": {
    "investor": "//div[@class='navbar-nav navbar-secondary-links']//a[contains(text(), 'Press Releases')]",
    "news": "//div[@class='navbar-nav navbar-secondary-links']//a[contains(text(), 'Recent News & Events')]",
    "general": ""
  },
  "confidence": 0.85,
  "page_structure": "页面主要由导航栏、内容区域和页脚组成。导航栏包含多个下拉菜单，分别指向投资者关系、新闻与事件等部分。内容区域包含委员会组成的表格，表格中包含董事会成员及其所属委员会的链接。页脚包含联系信息和法律声明。",
  "content_classification": "内容分类依据主要是通过导航栏的链接文本和结构来区分投资者关系和新闻内容。投资者关系链接主要集中在财务报告、治理文档等，而新闻内容则集中在公司新闻和媒体报道。",
  "sample_links": {
    "investor": [
      "https://trisaluslifesci.com/news-events/press-releases",
      "https://trisaluslifesci.com/news-events/events-presentations",
      "https://trisaluslifesci.com/governance/governance-overview",
      "https://trisaluslifesci.com/governance/board-diversity-matrix",
      "https://trisaluslifesci.com/financials/sec-filings"
    ],
    "news": [
      "https://trisaluslifesci.com/news-and-events/",
      "https://trisaluslifesci.com/press-media/"
    ]
  }
}
```
2025-07-03 08:19:11,789 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 08:19:11,789 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/governance/committee-composition 提取到: 投资者5个, 新闻2个, 通用0个XPath规则
2025-07-03 08:19:12,800 - src.core.search_research - INFO - 分析investor_relations页面: https://investors.trisaluslifesci.com/governance/board-diversity-matrix
2025-07-03 08:19:12,800 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/governance/board-diversity-matrix
2025-07-03 08:19:12,800 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/governance/board-diversity-matrix
2025-07-03 08:19:17,920 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /governance/board-diversity-matrix (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/governance/board-diversity-matrix
2025-07-03 08:19:18,925 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/governance/board-diversity-matrix
2025-07-03 08:19:24,078 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /governance/board-diversity-matrix (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/governance/board-diversity-matrix
2025-07-03 08:19:24,078 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/governance/board-diversity-matrix
2025-07-03 08:19:24,078 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/governance/board-diversity-matrix
2025-07-03 08:19:31,922 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/governance/board-diversity-matrix, 内容长度: 162561
2025-07-03 08:19:31,923 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 162,561 字符
2025-07-03 08:19:32,012 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 08:19:32,013 - src.services.web_scraper - INFO -   原始长度: 162,561 字符
2025-07-03 08:19:32,013 - src.services.web_scraper - INFO -   清理后长度: 17,662 字符
2025-07-03 08:19:32,013 - src.services.web_scraper - INFO -   减少比例: 89.1%
2025-07-03 08:19:32,013 - src.services.web_scraper - INFO -   清理统计: 移除9个标签, 82个属性, 6个注释, 20个空标签
2025-07-03 08:19:32,013 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/governance/board-diversity-matrix, 内容长度: 17662
2025-07-03 08:19:32,013 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 17,662 字符
2025-07-03 08:19:32,064 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 08:19:32,064 - src.services.web_scraper - INFO -   原始长度: 17,662 字符
2025-07-03 08:19:32,065 - src.services.web_scraper - INFO -   清理后长度: 17,183 字符
2025-07-03 08:19:32,065 - src.services.web_scraper - INFO -   减少比例: 2.7%
2025-07-03 08:19:32,065 - src.services.web_scraper - INFO -   清理统计: 移除0个标签, 0个属性, 0个注释, 3个空标签
2025-07-03 08:19:32,065 - src.core.search_research - INFO - 页面内容适中(17,183字符)，使用单次处理: https://investors.trisaluslifesci.com/governance/board-diversity-matrix
2025-07-03 08:19:32,065 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 08:19:37,140 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 1476
2025-07-03 08:19:37,140 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 08:19:37,141 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_xpath_rules": [
    "//ul[@class='navbar-nav navbar-secondary-links']//a[contains(@href, '/investor-relations')]",
    "//ul[@class='navbar-nav navbar-secondary-links']//a[contains(@href, '/financials')]",
    "//ul[@class='navbar-nav navbar-secondary-links']//a[contains(@href, '/governance')]"
  ],
  "news_xpath_rules": [
    "//ul[@class='navbar-nav navbar-secondary-links']//a[contains(@href, '/news-and-events')]",
    "//ul[@class='navbar-nav navbar-secondary-links']//a[contains(@href, '/press-media')]"
  ],
  "general_xpath_rules": [],
  "primary_xpath": {
    "investor": "//ul[@class='navbar-nav navbar-secondary-links']//a[contains(@href, '/investor-relations')]",
    "news": "//ul[@class='navbar-nav navbar-secondary-links']//a[contains(@href, '/news-and-events')]",
    "general": ""
  },
  "confidence": 0.9,
  "page_structure": "页面主要由导航栏、内容区域和页脚组成。导航栏包含多个下拉菜单，分别链接到投资者关系、新闻与事件、文化等部分。内容区域包含一个标题和一个表格，表格展示了董事会多样性矩阵。页脚包含版权信息和其他链接。",
  "content_classification": "内容分类依据主要是链接的URL结构和上下文。投资者关系链接通常包含'/investor-relations'或'/financials'，而新闻链接则包含'/news-and-events'或'/press-media'。",
  "sample_links": {
    "investor": [
      "https://trisaluslifesci.com/investor-relations",
      "https://trisaluslifesci.com/financials/sec-filings",
      "https://trisaluslifesci.com/governance/board-diversity-matrix"
    ],
    "news": [
      "https://trisaluslifesci.com/news-and-events/",
      "https://trisaluslifesci.com/press-media/"
    ]
  }
}
```
2025-07-03 08:19:37,141 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 08:19:37,141 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/governance/board-diversity-matrix 提取到: 投资者3个, 新闻2个, 通用0个XPath规则
2025-07-03 08:19:38,141 - src.core.search_research - INFO - 分析investor_relations页面: https://investors.trisaluslifesci.com/stock-information/analyst-coverage
2025-07-03 08:19:38,141 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/stock-information/analyst-coverage
2025-07-03 08:19:38,141 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/stock-information/analyst-coverage
2025-07-03 08:19:43,251 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /stock-information/analyst-coverage (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/stock-information/analyst-coverage
2025-07-03 08:19:44,264 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/stock-information/analyst-coverage
2025-07-03 08:19:49,371 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /stock-information/analyst-coverage (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/stock-information/analyst-coverage
2025-07-03 08:19:49,371 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/stock-information/analyst-coverage
2025-07-03 08:19:49,371 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/stock-information/analyst-coverage
2025-07-03 08:19:57,267 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/stock-information/analyst-coverage, 内容长度: 162628
2025-07-03 08:19:57,267 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 162,628 字符
2025-07-03 08:19:57,347 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 08:19:57,347 - src.services.web_scraper - INFO -   原始长度: 162,628 字符
2025-07-03 08:19:57,347 - src.services.web_scraper - INFO -   清理后长度: 17,527 字符
2025-07-03 08:19:57,347 - src.services.web_scraper - INFO -   减少比例: 89.2%
2025-07-03 08:19:57,348 - src.services.web_scraper - INFO -   清理统计: 移除8个标签, 76个属性, 6个注释, 16个空标签
2025-07-03 08:19:57,348 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/stock-information/analyst-coverage, 内容长度: 17527
2025-07-03 08:19:57,348 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 17,527 字符
2025-07-03 08:19:57,388 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 08:19:57,388 - src.services.web_scraper - INFO -   原始长度: 17,527 字符
2025-07-03 08:19:57,388 - src.services.web_scraper - INFO -   清理后长度: 17,048 字符
2025-07-03 08:19:57,388 - src.services.web_scraper - INFO -   减少比例: 2.7%
2025-07-03 08:19:57,388 - src.services.web_scraper - INFO -   清理统计: 移除0个标签, 0个属性, 0个注释, 3个空标签
2025-07-03 08:19:57,388 - src.core.search_research - INFO - 页面内容适中(17,048字符)，使用单次处理: https://investors.trisaluslifesci.com/stock-information/analyst-coverage
2025-07-03 08:19:57,389 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 08:20:02,188 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 1905
2025-07-03 08:20:02,188 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 08:20:02,188 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_xpath_rules": [
    "//div[@class='block--analyst-starter']//table//td//div[@class='field field--name-field-nir-person-company']//following-sibling::td//a",
    "//ul[@class='navbar-nav navbar-secondary-links']//li[contains(a/text(), 'News & Events')]//a[@href]",
    "//div[@class='block--attribution-block__analysts-disclaimer__nasdaq']//p"
  ],
  "news_xpath_rules": [
    "//ul[@class='navbar-nav navbar-secondary-links']//li[contains(a/text(), 'Recent News & Events')]//a[@href]",
    "//div[@class='nir-widget--list']//table//tbody//tr//td//div[@class='field field--name-field-nir-person-company']//following-sibling::td",
    "//div[@class='nir-widget--content']//div[contains(@class, 'nir-widget--list')]//table//tbody//tr//td//a"
  ],
  "general_xpath_rules": [
    "//div[@class='region region-content']//article//a[@href]",
    "//section[@class='top-section']//h1",
    "//div[@class='c-calltoaction__contact']//a[@href]"
  ],
  "primary_xpath": {
    "investor": "//div[@class='block--analyst-starter']//table//td//div[@class='field field--name-field-nir-person-company']//following-sibling::td//a",
    "news": "//ul[@class='navbar-nav navbar-secondary-links']//li[contains(a/text(), 'Recent News & Events')]//a[@href]",
    "general": "//div[@class='region region-content']//article//a[@href]"
  },
  "confidence": 0.85,
  "page_structure": "页面包含多个导航栏和内容区域，主要内容在一个大的div中，包含多个section和article元素。新闻和投资者信息通过不同的class和结构区分，使用table和ul/li结构展示。",
  "content_classification": "内容分类依据主要是通过导航链接和内容区域的class属性来区分投资者关系信息和新闻信息。投资者信息主要集中在分析师覆盖和财务报告相关内容，而新闻信息则集中在新闻和事件的链接上。",
  "sample_links": {
    "investor": [
      "https://trisaluslifesci.com/news-events/press-releases",
      "https://trisaluslifesci.com/news-events/events-presentations"
    ],
    "news": [
      "https://trisaluslifesci.com/news-and-events/",
      "https://trisaluslifesci.com/press-media/"
    ]
  }
}
```
2025-07-03 08:20:02,189 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 08:20:02,189 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/stock-information/analyst-coverage 提取到: 投资者3个, 新闻3个, 通用3个XPath规则
2025-07-03 08:20:03,190 - src.core.search_research - INFO - 分析investor_relations页面: https://investors.trisaluslifesci.com/investor-resources/investor-contacts
2025-07-03 08:20:03,190 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/investor-resources/investor-contacts
2025-07-03 08:20:03,190 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/investor-resources/investor-contacts
2025-07-03 08:20:08,298 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /investor-resources/investor-contacts (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/investor-resources/investor-contacts
2025-07-03 08:20:09,303 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/investor-resources/investor-contacts
2025-07-03 08:20:14,445 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /investor-resources/investor-contacts (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/investor-resources/investor-contacts
2025-07-03 08:20:14,445 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/investor-resources/investor-contacts
2025-07-03 08:20:14,445 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/investor-resources/investor-contacts
2025-07-03 08:20:22,988 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/investor-resources/investor-contacts, 内容长度: 167996
2025-07-03 08:20:22,988 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 167,996 字符
2025-07-03 08:20:23,070 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 08:20:23,070 - src.services.web_scraper - INFO -   原始长度: 167,996 字符
2025-07-03 08:20:23,070 - src.services.web_scraper - INFO -   清理后长度: 18,937 字符
2025-07-03 08:20:23,070 - src.services.web_scraper - INFO -   减少比例: 88.7%
2025-07-03 08:20:23,070 - src.services.web_scraper - INFO -   清理统计: 移除11个标签, 101个属性, 6个注释, 21个空标签
2025-07-03 08:20:23,070 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/investor-resources/investor-contacts, 内容长度: 18937
2025-07-03 08:20:23,070 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 18,937 字符
2025-07-03 08:20:23,112 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 08:20:23,113 - src.services.web_scraper - INFO -   原始长度: 18,937 字符
2025-07-03 08:20:23,113 - src.services.web_scraper - INFO -   清理后长度: 18,458 字符
2025-07-03 08:20:23,113 - src.services.web_scraper - INFO -   减少比例: 2.5%
2025-07-03 08:20:23,113 - src.services.web_scraper - INFO -   清理统计: 移除0个标签, 0个属性, 0个注释, 3个空标签
2025-07-03 08:20:23,113 - src.core.search_research - INFO - 页面内容适中(18,458字符)，使用单次处理: https://investors.trisaluslifesci.com/investor-resources/investor-contacts
2025-07-03 08:20:23,113 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 08:20:28,008 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 1673
2025-07-03 08:20:28,008 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 08:20:28,008 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_xpath_rules": [
    "//ul[@class='navbar-nav navbar-secondary-links']//a[contains(text(), 'Press Releases')]",
    "//ul[@class='navbar-nav navbar-secondary-links']//a[contains(text(), 'Events & Presentations')]",
    "//a[contains(@href, '/financials/') or contains(@href, '/investor-resources/')]"
  ],
  "news_xpath_rules": [
    "//ul[@class='navbar-nav navbar-secondary-links']//a[contains(text(), 'Recent News & Events')]",
    "//ul[@class='navbar-nav navbar-secondary-links']//a[contains(text(), 'Press & Media')]",
    "//section//article//a[contains(@href, '/news/')]"
  ],
  "general_xpath_rules": [
    "//div[@class='c-calltoaction__contact']//a[contains(@href, 'mailto:')]",
    "//div[@class='c-calltoaction__text']//a[contains(@href, '/investor-resources/')]"
  ],
  "primary_xpath": {
    "investor": "//ul[@class='navbar-nav navbar-secondary-links']//a[contains(text(), 'Press Releases')]",
    "news": "//ul[@class='navbar-nav navbar-secondary-links']//a[contains(text(), 'Recent News & Events')]",
    "general": "//div[@class='c-calltoaction__contact']//a[contains(@href, 'mailto:')]"
  },
  "confidence": 0.85,
  "page_structure": "页面包含多个导航菜单和内容区域，主要分为投资者关系和新闻部分。投资者关系链接位于二级导航中，新闻链接也在同一导航中。内容区域包括联系信息和订阅功能，使用了多个div和ul结构。",
  "content_classification": "内容分类依据主要是链接文本和href属性，投资者关系链接通常包含财务和报告相关的关键词，而新闻链接则包含新闻和事件相关的关键词。",
  "sample_links": {
    "investor": [
      "https://trisaluslifesci.com/news-events/press-releases",
      "https://trisaluslifesci.com/news-events/events-presentations"
    ],
    "news": [
      "https://trisaluslifesci.com/news-and-events/",
      "https://trisaluslifesci.com/press-media/"
    ]
  }
}
```
2025-07-03 08:20:28,008 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 08:20:28,009 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/investor-resources/investor-contacts 提取到: 投资者3个, 新闻3个, 通用2个XPath规则
2025-07-03 08:20:29,017 - src.core.search_research - INFO - 分析news_section页面: https://trisaluslifesci.com/news-and-events/
2025-07-03 08:20:29,017 - src.services.web_scraper - INFO - 开始抓取页面: https://trisaluslifesci.com/news-and-events/
2025-07-03 08:20:29,017 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://trisaluslifesci.com/news-and-events/
2025-07-03 08:20:29,197 - src.services.web_scraper - WARNING - requests页面抓取失败，状态码: 403, URL: https://trisaluslifesci.com/news-and-events/
2025-07-03 08:20:30,202 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://trisaluslifesci.com/news-and-events/
2025-07-03 08:20:30,380 - src.services.web_scraper - WARNING - requests页面抓取失败，状态码: 403, URL: https://trisaluslifesci.com/news-and-events/
2025-07-03 08:20:30,380 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://trisaluslifesci.com/news-and-events/
2025-07-03 08:20:30,380 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://trisaluslifesci.com/news-and-events/
2025-07-03 08:20:39,198 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://trisaluslifesci.com/news-and-events/, 内容长度: 204484
2025-07-03 08:20:39,198 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 204,484 字符
2025-07-03 08:20:39,437 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 08:20:39,437 - src.services.web_scraper - INFO -   原始长度: 204,484 字符
2025-07-03 08:20:39,437 - src.services.web_scraper - INFO -   清理后长度: 84,314 字符
2025-07-03 08:20:39,437 - src.services.web_scraper - INFO -   减少比例: 58.8%
2025-07-03 08:20:39,437 - src.services.web_scraper - INFO -   清理统计: 移除49个标签, 711个属性, 2个注释, 111个空标签
2025-07-03 08:20:39,437 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://trisaluslifesci.com/news-and-events/, 内容长度: 84314
2025-07-03 08:20:39,438 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 84,314 字符
2025-07-03 08:20:39,563 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 08:20:39,563 - src.services.web_scraper - INFO -   原始长度: 84,314 字符
2025-07-03 08:20:39,563 - src.services.web_scraper - INFO -   清理后长度: 83,335 字符
2025-07-03 08:20:39,563 - src.services.web_scraper - INFO -   减少比例: 1.2%
2025-07-03 08:20:39,563 - src.services.web_scraper - INFO -   清理统计: 移除0个标签, 0个属性, 0个注释, 9个空标签
2025-07-03 08:20:39,563 - src.core.search_research - INFO - 页面内容过长(83,335字符)，使用分批处理: https://trisaluslifesci.com/news-and-events/
2025-07-03 08:20:39,563 - src.services.web_scraper - INFO - HTML内容过长(83,335字符)，开始智能分割(阈值: 50,000)
2025-07-03 08:20:39,625 - src.services.web_scraper - INFO - 使用选择器 'section' 成功分割为 3 个分片
2025-07-03 08:20:39,625 - src.services.web_scraper - INFO - HTML内容分割完成，共生成 3 个分片
2025-07-03 08:20:39,625 - src.services.ai_analyzer - INFO - 开始分批分析分类XPath规则，页面类型: news_section，共 3 个分片
2025-07-03 08:20:39,626 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 08:20:46,081 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 2011
2025-07-03 08:20:46,081 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 08:20:46,081 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_xpath_rules": [
    "//ul[@class='elementor-nav-menu']//li[contains(@class, 'menu-item-has-children') and contains(a/text(), 'News & Events')]//ul//li//a[@href]",
    "//ul[@class='elementor-nav-menu']//li[contains(@class, 'menu-item-has-children') and contains(a/text(), 'Financials')]//ul//li//a[@href]",
    "//ul[@class='elementor-nav-menu']//li[contains(@class, 'menu-item-has-children') and contains(a/text(), 'Governance')]//ul//li//a[@href]"
  ],
  "news_xpath_rules": [
    "//div[@class='elementor-widget-wrap']//a[contains(@href, '/news-and-events/')]",
    "//ul[@class='elementor-nav-menu']//li[contains(@class, 'menu-item') and contains(a/text(), 'Recent News & Events')]//a[@href]",
    "//ul[@class='elementor-nav-menu']//li[contains(@class, 'menu-item') and contains(a/text(), 'Press & Media')]//a[@href]"
  ],
  "general_xpath_rules": [
    "//div[@class='elementor-widget-wrap']//a[@href]",
    "//section[@class='has_ae_slider']//a[@href]"
  ],
  "primary_xpath": {
    "investor": "//ul[@class='elementor-nav-menu']//li[contains(@class, 'menu-item-has-children') and contains(a/text(), 'News & Events')]//ul//li//a[@href]",
    "news": "//div[@class='elementor-widget-wrap']//a[contains(@href, '/news-and-events/')]",
    "general": "//div[@class='elementor-widget-wrap']//a[@href]"
  },
  "confidence": 0.85,
  "page_structure": "页面包含一个主要的<section>元素，内部有多个<div>和<ul>元素，包含导航菜单和新闻链接。新闻链接通常在<ul>列表中，包含<a>标签指向具体的新闻页面。",
  "content_classification": "内容分类依据主要是链接文本和结构，投资者关系内容通常包含财务和治理相关的链接，而新闻内容则集中在公司新闻和媒体报道上。",
  "sample_links": {
    "investor": [
      "https://investors.trisaluslifesci.com/news-events/press-releases",
      "https://investors.trisaluslifesci.com/financials/sec-filings",
      "https://investors.trisaluslifesci.com/governance/governance-overview"
    ],
    "news": [
      "https://trisaluslifesci.com/news-and-events/",
      "https://trisaluslifesci.com/press-media/",
      "https://trisaluslifesci.com/news/product-launch-2024"
    ]
  }
}
```
2025-07-03 08:20:46,082 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 08:20:46,082 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 08:20:51,655 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 1800
2025-07-03 08:20:51,655 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 08:20:51,655 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_xpath_rules": [
    "//div[contains(@class, 'news-feed')]//h3/a[@href]",
    "//section[@id='events']//h4/a[@href]",
    "//div[@class='elementor-widget-container']//a[contains(@href, 'investors.trisaluslifesci.com')]"
  ],
  "news_xpath_rules": [
    "//div[contains(@class, 'news-feed')]//h3/a[@href]",
    "//section[contains(@class, 'has_ae_slider')]//h4/a[@href]",
    "//div[@class='elementor-widget-container']//a[contains(@href, 'trisaluslifesci.com/news')]"
  ],
  "general_xpath_rules": [
    "//div[contains(@class, 'elementor-widget-container')]//a[@href]",
    "//section[contains(@class, 'has_ae_slider')]//a[@href]"
  ],
  "primary_xpath": {
    "investor": "//div[contains(@class, 'news-feed')]//h3/a[@href]",
    "news": "//div[contains(@class, 'news-feed')]//h3/a[@href]",
    "general": "//div[contains(@class, 'elementor-widget-container')]//a[@href]"
  },
  "confidence": 0.85,
  "page_structure": "页面包含多个<section>元素，每个<section>包含不同的内容区域。新闻和事件信息主要在<div class='news-feed'>中列出，包含<h3>作为标题和<a>作为链接。投资者相关信息则在包含投资者新闻的<div>中，通常包含指向投资者关系页面的链接。",
  "content_classification": "内容分类依据主要是通过HTML结构和类名来区分不同类型的内容区域。投资者关系内容通常包含在特定的<div>中，而新闻内容则在另一个<div>中，且有不同的标题和链接结构。",
  "sample_links": {
    "investor": [
      "https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-commencement-exchange-offer-0",
      "https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-launch-trinav-flx-infusion",
      "https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-chief-financial-officer"
    ],
    "news": [
      "https://trisaluslifesci.com/news-and-events/",
      "https://trisaluslifesci.com/press-media/"
    ]
  }
}
```
2025-07-03 08:20:51,655 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 08:20:51,656 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 08:20:56,400 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 1223
2025-07-03 08:20:56,400 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 08:20:56,400 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_xpath_rules": [],
  "news_xpath_rules": [
    "//section[contains(@class, 'elementor-section')]//h4//following-sibling::p//a[@href]",
    "//section[contains(@class, 'elementor-section')]//h4//a[@href]",
    "//section[contains(@class, 'elementor-section')]//h4//parent::div//following-sibling::div[contains(@class, 'elementor-widget-text-editor')]//a[@href]"
  ],
  "general_xpath_rules": [],
  "primary_xpath": {
    "investor": "",
    "news": "//section[contains(@class, 'elementor-section')]//h4//following-sibling::p//a[@href]",
    "general": ""
  },
  "confidence": 0.9,
  "page_structure": "页面包含多个<section>元素，每个<section>代表一个新闻条目。每个条目包含一个标题(h4)、一段描述(p)和可能的链接(a)。这些元素被嵌套在具有特定类名的div中，形成一个结构化的新闻列表。",
  "content_classification": "页面主要包含公司新闻和活动信息，符合一般新闻页面的特征。没有投资者关系相关的内容，因此没有相关XPath规则。",
  "sample_links": {
    "investor": [],
    "news": [
      "https://trisaluslifesci.com/recent-news/trisalus-life-sciences-to-showcase-treatment-platform-and-present-pressure-enabled-regional-immuno-oncology-perio-data-at-the-society-of-interventional-radiologys-2023-annual-scientific-mee/",
      "http://www.sio-central.org/p/cm/ld/fid=811",
      "https://conferences.asco.org/gi/attend"
    ]
  }
}
```
2025-07-03 08:20:56,400 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 08:20:56,400 - src.services.ai_analyzer - INFO - 分批分类XPath分析完成:
2025-07-03 08:20:56,400 - src.services.ai_analyzer - INFO -   投资者XPath: 6 -> 3 个唯一规则
2025-07-03 08:20:56,400 - src.services.ai_analyzer - INFO -   新闻XPath: 8 -> 3 个唯一规则
2025-07-03 08:20:56,400 - src.services.ai_analyzer - INFO -   通用XPath: 4 -> 2 个唯一规则
2025-07-03 08:20:56,400 - src.services.ai_analyzer - INFO -   平均置信度: 0.87
2025-07-03 08:20:56,400 - src.core.search_research - INFO - 从 https://trisaluslifesci.com/news-and-events/ 提取到: 投资者3个, 新闻3个, 通用2个XPath规则
2025-07-03 08:20:56,400 - src.core.search_research - INFO - 分批处理结果: {'total_fragments': 3, 'valid_results': 3, 'total_investor_found': 6, 'total_news_found': 8, 'total_general_found': 4, 'unique_investor': 3, 'unique_news': 3, 'unique_general': 2, 'unique_urls': 1}
2025-07-03 08:20:57,410 - src.core.search_research - INFO - 分析news_section页面: https://trisaluslifesci.com/press-media/
2025-07-03 08:20:57,410 - src.services.web_scraper - INFO - 开始抓取页面: https://trisaluslifesci.com/press-media/
2025-07-03 08:20:57,410 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://trisaluslifesci.com/press-media/
2025-07-03 08:20:57,574 - src.services.web_scraper - WARNING - requests页面抓取失败，状态码: 403, URL: https://trisaluslifesci.com/press-media/
2025-07-03 08:20:58,582 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://trisaluslifesci.com/press-media/
2025-07-03 08:20:58,752 - src.services.web_scraper - WARNING - requests页面抓取失败，状态码: 403, URL: https://trisaluslifesci.com/press-media/
2025-07-03 08:20:58,752 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://trisaluslifesci.com/press-media/
2025-07-03 08:20:58,752 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://trisaluslifesci.com/press-media/
2025-07-03 08:21:06,810 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://trisaluslifesci.com/press-media/, 内容长度: 194151
2025-07-03 08:21:06,810 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 194,151 字符
2025-07-03 08:21:06,957 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 08:21:06,958 - src.services.web_scraper - INFO -   原始长度: 194,151 字符
2025-07-03 08:21:06,958 - src.services.web_scraper - INFO -   清理后长度: 35,090 字符
2025-07-03 08:21:06,958 - src.services.web_scraper - INFO -   减少比例: 81.9%
2025-07-03 08:21:06,958 - src.services.web_scraper - INFO -   清理统计: 移除9个标签, 112个属性, 6个注释, 27个空标签
2025-07-03 08:21:06,958 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://trisaluslifesci.com/press-media/, 内容长度: 35090
2025-07-03 08:21:06,958 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 35,090 字符
2025-07-03 08:21:07,035 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 08:21:07,035 - src.services.web_scraper - INFO -   原始长度: 35,090 字符
2025-07-03 08:21:07,035 - src.services.web_scraper - INFO -   清理后长度: 34,611 字符
2025-07-03 08:21:07,035 - src.services.web_scraper - INFO -   减少比例: 1.4%
2025-07-03 08:21:07,035 - src.services.web_scraper - INFO -   清理统计: 移除0个标签, 0个属性, 0个注释, 3个空标签
2025-07-03 08:21:07,035 - src.core.search_research - INFO - 页面内容适中(34,611字符)，使用单次处理: https://trisaluslifesci.com/press-media/
2025-07-03 08:21:07,036 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 08:21:12,100 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 1468
2025-07-03 08:21:12,100 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 08:21:12,100 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_xpath_rules": [],
  "news_xpath_rules": [
    "//article[contains(@class, 'node--nir-news')]//div[contains(@class, 'nir-widget--news--headline')]//a[@href]",
    "//div[@class='nir-widget--list']//article//a[@href]",
    "//div[@class='nir-widget--content']//article//a[@href]"
  ],
  "general_xpath_rules": [],
  "primary_xpath": {
    "investor": "",
    "news": "//article[contains(@class, 'node--nir-news')]//div[contains(@class, 'nir-widget--news--headline')]//a[@href]",
    "general": ""
  },
  "confidence": 0.9,
  "page_structure": "该页面主要由导航栏、新闻列表和页脚组成。新闻列表位于主要内容区域，包含多个新闻条目，每个条目由一个article元素表示，内部包含新闻标题、发布日期和摘要等信息。",
  "content_classification": "页面主要包含新闻内容，未发现投资者关系相关内容。新闻内容通过article元素和特定的class属性进行分类。",
  "sample_links": {
    "investor": [],
    "news": [
      "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-commencement-exchange-offer-0",
      "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-launch-trinav-flx-infusion",
      "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-chief-financial-officer",
      "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-reports-first-quarter-2025-results-and",
      "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-220-million-private-placement"
    ]
  }
}
```
2025-07-03 08:21:12,100 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 08:21:12,100 - src.core.search_research - INFO - 从 https://trisaluslifesci.com/press-media/ 提取到: 投资者0个, 新闻3个, 通用0个XPath规则
2025-07-03 08:21:13,105 - src.core.search_research - INFO - 分析news_section页面: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-commencement-exchange-offer-0
2025-07-03 08:21:13,105 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-commencement-exchange-offer-0
2025-07-03 08:21:13,105 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-commencement-exchange-offer-0
2025-07-03 08:21:18,215 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /news-releases/news-release-details/trisalus-life-sciences-announces-commencement-exchange-offer-0 (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-commencement-exchange-offer-0
2025-07-03 08:21:19,225 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-commencement-exchange-offer-0
2025-07-03 08:21:24,329 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /news-releases/news-release-details/trisalus-life-sciences-announces-commencement-exchange-offer-0 (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-commencement-exchange-offer-0
2025-07-03 08:21:24,329 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-commencement-exchange-offer-0
2025-07-03 08:21:24,329 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-commencement-exchange-offer-0
2025-07-03 08:21:32,245 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-commencement-exchange-offer-0, 内容长度: 176136
2025-07-03 08:21:32,245 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 176,136 字符
2025-07-03 08:21:32,342 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 08:21:32,342 - src.services.web_scraper - INFO -   原始长度: 176,136 字符
2025-07-03 08:21:32,342 - src.services.web_scraper - INFO -   清理后长度: 30,713 字符
2025-07-03 08:21:32,342 - src.services.web_scraper - INFO -   减少比例: 82.6%
2025-07-03 08:21:32,342 - src.services.web_scraper - INFO -   清理统计: 移除8个标签, 69个属性, 5个注释, 15个空标签
2025-07-03 08:21:32,342 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-commencement-exchange-offer-0, 内容长度: 30713
2025-07-03 08:21:32,342 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 30,713 字符
2025-07-03 08:21:32,398 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 08:21:32,400 - src.services.web_scraper - INFO -   原始长度: 30,713 字符
2025-07-03 08:21:32,400 - src.services.web_scraper - INFO -   清理后长度: 30,234 字符
2025-07-03 08:21:32,400 - src.services.web_scraper - INFO -   减少比例: 1.6%
2025-07-03 08:21:32,400 - src.services.web_scraper - INFO -   清理统计: 移除0个标签, 0个属性, 0个注释, 3个空标签
2025-07-03 08:21:32,400 - src.core.search_research - INFO - 页面内容适中(30,234字符)，使用单次处理: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-commencement-exchange-offer-0
2025-07-03 08:21:32,400 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 08:21:36,140 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 828
2025-07-03 08:21:36,140 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 08:21:36,140 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_xpath_rules": [],
  "news_xpath_rules": [
    "//article[@class='node node--nir-news--full']//h2//div[@class='field__item']/a",
    "//div[@class='node__content']//p//a[contains(@href, '/node/')]",
    "//div[@class='full-release-body']//p//a[contains(@href, 'businesswire.com')]"
  ],
  "general_xpath_rules": [],
  "primary_xpath": {
    "investor": "",
    "news": "//article[@class='node node--nir-news--full']//h2//div[@class='field__item']/a",
    "general": ""
  },
  "confidence": 0.9,
  "page_structure": "页面主要由一个包含新闻详细信息的article元素构成，包含新闻标题、发布日期、正文内容和相关链接。新闻标题在h2标签内，发布日期和正文内容在div和p标签中。",
  "content_classification": "页面主要内容为新闻发布，包含新闻标题、发布日期和详细内容，符合一般新闻页面的特征。",
  "sample_links": {
    "investor": [],
    "news": [
      "https://www.businesswire.com/news/home/<USER>/en/"
    ]
  }
}
```
2025-07-03 08:21:36,141 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 08:21:36,141 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-commencement-exchange-offer-0 提取到: 投资者0个, 新闻3个, 通用0个XPath规则
2025-07-03 08:21:37,144 - src.core.search_research - INFO - 分析news_section页面: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-launch-trinav-flx-infusion
2025-07-03 08:21:37,144 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-launch-trinav-flx-infusion
2025-07-03 08:21:37,144 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-launch-trinav-flx-infusion
2025-07-03 08:21:42,277 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /news-releases/news-release-details/trisalus-life-sciences-announces-launch-trinav-flx-infusion (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-launch-trinav-flx-infusion
2025-07-03 08:21:43,285 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-launch-trinav-flx-infusion
2025-07-03 08:21:48,395 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /news-releases/news-release-details/trisalus-life-sciences-announces-launch-trinav-flx-infusion (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-launch-trinav-flx-infusion
2025-07-03 08:21:48,395 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-launch-trinav-flx-infusion
2025-07-03 08:21:48,395 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-launch-trinav-flx-infusion
2025-07-03 08:21:56,208 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-launch-trinav-flx-infusion, 内容长度: 170234
2025-07-03 08:21:56,208 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 170,234 字符
2025-07-03 08:21:56,297 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 08:21:56,297 - src.services.web_scraper - INFO -   原始长度: 170,234 字符
2025-07-03 08:21:56,297 - src.services.web_scraper - INFO -   清理后长度: 25,153 字符
2025-07-03 08:21:56,298 - src.services.web_scraper - INFO -   减少比例: 85.2%
2025-07-03 08:21:56,298 - src.services.web_scraper - INFO -   清理统计: 移除8个标签, 69个属性, 5个注释, 16个空标签
2025-07-03 08:21:56,298 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-launch-trinav-flx-infusion, 内容长度: 25153
2025-07-03 08:21:56,298 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 25,153 字符
2025-07-03 08:21:56,349 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 08:21:56,349 - src.services.web_scraper - INFO -   原始长度: 25,153 字符
2025-07-03 08:21:56,349 - src.services.web_scraper - INFO -   清理后长度: 24,674 字符
2025-07-03 08:21:56,349 - src.services.web_scraper - INFO -   减少比例: 1.9%
2025-07-03 08:21:56,349 - src.services.web_scraper - INFO -   清理统计: 移除0个标签, 0个属性, 0个注释, 3个空标签
2025-07-03 08:21:56,349 - src.core.search_research - INFO - 页面内容适中(24,674字符)，使用单次处理: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-launch-trinav-flx-infusion
2025-07-03 08:21:56,349 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 08:22:00,133 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 908
2025-07-03 08:22:00,133 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 08:22:00,133 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_xpath_rules": [],
  "news_xpath_rules": [
    "//article[@class='node node--nir-news--full']//h2//div[@class='field__item']/a",
    "//div[@class='node__content']//p/a[contains(@href, 'businesswire.com')]",
    "//div[@class='node__content']//p/a[contains(@href, 'trinavinfusion.com')]"
  ],
  "general_xpath_rules": [],
  "primary_xpath": {
    "investor": "",
    "news": "//article[@class='node node--nir-news--full']//h2//div[@class='field__item']/a",
    "general": ""
  },
  "confidence": 0.9,
  "page_structure": "页面主要由一个包含新闻详细信息的article元素构成，包含新闻标题、发布日期、正文内容和相关链接。新闻标题在h2标签内，发布日期和正文内容在div元素中，链接通常在p标签内的a标签中。",
  "content_classification": "页面主要内容为新闻发布，包含公司新闻和产品发布信息，符合一般新闻页面的特征。没有投资者关系相关内容，因此投资者XPath规则为空。",
  "sample_links": {
    "investor": [],
    "news": [
      "https://www.businesswire.com/news/home/<USER>/en/",
      "https://trinavinfusion.com/"
    ]
  }
}
```
2025-07-03 08:22:00,133 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 08:22:00,133 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-launch-trinav-flx-infusion 提取到: 投资者0个, 新闻3个, 通用0个XPath规则
2025-07-03 08:22:01,139 - src.core.search_research - INFO - 分析news_section页面: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-chief-financial-officer
2025-07-03 08:22:01,139 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-chief-financial-officer
2025-07-03 08:22:01,139 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-chief-financial-officer
2025-07-03 08:22:06,259 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /news-releases/news-release-details/trisalus-life-sciences-announces-chief-financial-officer (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-chief-financial-officer
2025-07-03 08:22:07,269 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-chief-financial-officer
2025-07-03 08:22:12,389 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /news-releases/news-release-details/trisalus-life-sciences-announces-chief-financial-officer (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-chief-financial-officer
2025-07-03 08:22:12,389 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-chief-financial-officer
2025-07-03 08:22:12,389 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-chief-financial-officer
2025-07-03 08:22:20,297 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-chief-financial-officer, 内容长度: 168872
2025-07-03 08:22:20,297 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 168,872 字符
2025-07-03 08:22:20,387 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 08:22:20,387 - src.services.web_scraper - INFO -   原始长度: 168,872 字符
2025-07-03 08:22:20,387 - src.services.web_scraper - INFO -   清理后长度: 23,876 字符
2025-07-03 08:22:20,388 - src.services.web_scraper - INFO -   减少比例: 85.9%
2025-07-03 08:22:20,388 - src.services.web_scraper - INFO -   清理统计: 移除8个标签, 69个属性, 5个注释, 15个空标签
2025-07-03 08:22:20,388 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-chief-financial-officer, 内容长度: 23876
2025-07-03 08:22:20,388 - src.services.web_scraper - INFO - 开始HTML深度清理，原始长度: 23,876 字符
2025-07-03 08:22:20,442 - src.services.web_scraper - INFO - HTML内容深度清理完成:
2025-07-03 08:22:20,443 - src.services.web_scraper - INFO -   原始长度: 23,876 字符
2025-07-03 08:22:20,443 - src.services.web_scraper - INFO -   清理后长度: 23,397 字符
2025-07-03 08:22:20,443 - src.services.web_scraper - INFO -   减少比例: 2.0%
2025-07-03 08:22:20,443 - src.services.web_scraper - INFO -   清理统计: 移除0个标签, 0个属性, 0个注释, 3个空标签
2025-07-03 08:22:20,443 - src.core.search_research - INFO - 页面内容适中(23,397字符)，使用单次处理: https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-chief-financial-officer
2025-07-03 08:22:20,443 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-03 08:22:24,390 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 958
2025-07-03 08:22:24,391 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-03 08:22:24,391 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_xpath_rules": [],
  "news_xpath_rules": [
    "//article[@class='node node--nir-news--full']//h2//div[@class='field__item']/a",
    "//div[@id='lfg-main-content']//article//h2//div[@class='field__item']/a",
    "//div[@class='node__content']//p[contains(text(),'Source:')]/preceding-sibling::a"
  ],
  "general_xpath_rules": [],
  "primary_xpath": {
    "investor": "",
    "news": "//article[@class='node node--nir-news--full']//h2//div[@class='field__item']/a",
    "general": ""
  },
  "confidence": 0.85,
  "page_structure": "该页面主要由一个包含新闻详细信息的article元素组成，包含新闻标题、发布日期和内容。新闻标题在h2标签内，发布日期和其他信息在相应的div和p标签中。没有明显的投资者关系内容或链接。",
  "content_classification": "页面主要包含新闻发布信息，未包含投资者关系相关内容。新闻内容通过article标签和h2标签进行组织，日期信息以标准格式显示在相应的div中。",
  "sample_links": {
    "investor": [],
    "news": [
      "https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-chief-financial-officer"
    ]
  }
}
```
2025-07-03 08:22:24,391 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-03 08:22:24,391 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-chief-financial-officer 提取到: 投资者0个, 新闻3个, 通用0个XPath规则
2025-07-03 08:22:25,403 - src.core.search_research - INFO - 分类XPath规则提取完成:
2025-07-03 08:22:25,403 - src.core.search_research - INFO -   投资者XPath: 41 个
2025-07-03 08:22:25,403 - src.core.search_research - INFO -   新闻XPath: 37 个
2025-07-03 08:22:25,404 - src.core.search_research - INFO -   通用XPath: 15 个
2025-07-03 08:22:25,404 - src.core.search_research - INFO -   涉及URL数量: 18 个
2025-07-03 08:22:25,404 - src.core.search_research - INFO - 提取到投资者XPath规则: 41个
2025-07-03 08:22:25,404 - src.core.search_research - INFO - 提取到新闻XPath规则: 37个
2025-07-03 08:22:25,404 - src.core.search_research - INFO - 提取到通用XPath规则: 15个
2025-07-03 08:22:25,404 - src.core.search_research - INFO - 总计XPath规则: 93个
2025-07-03 08:22:25,404 - src.core.search_research - INFO - 涉及URL数量: 18个
2025-07-03 08:22:25,404 - src.core.search_research - INFO - 公司调研完成: TriSalus
2025-07-03 08:22:26,454 - src.services.drission_scraper - INFO - DrissionPage浏览器实例已关闭
2025-07-03 08:22:26,454 - src.services.web_scraper - INFO - DrissionPage资源已清理
2025-07-03 08:22:26,454 - __main__ - INFO - ✓ TriSalus 调研完成
2025-07-03 08:22:26,458 - __main__ - ERROR - 保存结果文件失败: Object of type ClassifiedXPathResult is not JSON serializable
2025-07-03 08:22:26,458 - __main__ - INFO - 所有公司调研完成
2025-07-03 08:40:20,413 - main - INFO - 调研结果已保存到: test_cleaned_result.json
